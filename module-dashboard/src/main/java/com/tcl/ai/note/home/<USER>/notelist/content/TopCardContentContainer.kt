package com.tcl.ai.note.home.components.notelist.content

import android.annotation.SuppressLint
import android.view.MotionEvent
import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.PointerType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.widget.clickableHover
import com.tcl.ai.note.widget.combinedClickable

/**
 * 顶部内容卡片 容器
 */
@SuppressLint("DesignSystem")
@Composable
internal fun TopCardContentContainer(
    modifier: Modifier,
    editMode: Boolean,
    onCheckedChange: ((Boolean) -> Unit)?,
    checked: Boolean,
    onItemClick: (String, Boolean) -> Unit,
    note: HomeNoteItemModel,
    onLongClick: (() -> Unit)?
) {
    var isPen by remember {
        mutableStateOf(false)
    }

    Card(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .pointerInput(note.noteId) {
                awaitPointerEventScope {
                    while (true) {
                        val event = awaitPointerEvent()
                        if (event.type == PointerEventType.Press) {
                            isPen = event.changes.any { it.type == PointerType.Stylus }
                            Logger.d("CarItemClick", "awaitPointerEventScope isPen  $isPen")
                        }
                    }
                }
            }
            .combinedClickable(
                onClick = {
                    Logger.d("CarItemClick", "onItemClick isPen $isPen")
                    if (editMode) {
                        onCheckedChange?.invoke(!checked)
                    } else {
                        Logger.d("CarItemClick", "onItemClick goto EditActivity ")
                        onItemClick.invoke(note.noteId, isPen)
                    }
                },
                onLongClick = {
                    onLongClick?.invoke()
                }
            ),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = colorResource(R.color.home_note_list_item_bg_color)),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            CardTypeContent(note)
            CardDecoration(note, editMode,checked, onCheckedChange )
        }

    }
}

/**
 * 卡片装饰组件
 */
@Composable
private fun BoxScope.CardDecoration(
    note: HomeNoteItemModel,
    editMode: Boolean,
    checked: Boolean,
    onCheckedChange: ((Boolean) -> Unit)?
) {
    // 书签图标
    if (note.categoryIcon != null) {
        val (icon, color) = note.categoryIcon
        Icon(
            painter = painterResource(R.drawable.ic_note_category_flag),
            contentDescription = null,
            modifier = Modifier
                .padding(end = 8.dp)
                .size(14.dp, 22.dp)
                .align(Alignment.TopEnd),
            color?.let { colorResource(it) } ?: Color.Transparent
        )
    }
    if (editMode) {
        Box(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(8.dp)
                .size(24.dp)
                .clip(CircleShape)  // 添加圆形裁剪
                .clickableHover(
                    onClick = {
                        if (onCheckedChange != null) {
                            onCheckedChange(!checked)
                        }
                    }
                )
        ) {
            Image(
                painter = painterResource(if (checked) R.drawable.ic_note_checkbox_selected else R.drawable.ic_note_checkbox_normal),
                contentDescription = if (checked) stringResource(R.string.checked_status) else stringResource(R.string.unchecked_status),
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}