package com.tcl.ai.note.handwritingtext.richtext.views;

import static java.lang.Math.min;

import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.ClipDescription;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PointF;
import android.text.Editable;
import android.text.InputType;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.AlignmentSpan;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import com.tcl.ai.note.handwritingtext.database.entity.Note;
import com.tcl.ai.note.handwritingtext.richtext.converter.RichTextStyleEntityToSpanConverter;
import com.tcl.ai.note.handwritingtext.richtext.data.StyleRange;
import com.tcl.ai.note.handwritingtext.richtext.history.RichTextEditOperation;
import com.tcl.ai.note.handwritingtext.richtext.history.RichTextUndoRedoManager;
import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener;
import com.tcl.ai.note.handwritingtext.richtext.spans.AreUnderlineSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListBulletSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Alignment;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_BackgroundColor;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Bold;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontColor;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontSize;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Italic;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListBullet;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListNumber;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Strikethrough;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Underline;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming;
import com.tcl.ai.note.handwritingtext.richtext.styles.IARE_Style;
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils;
import com.tcl.ai.note.handwritingtext.richtext.utils.InputConnectionCEWrapper;
import com.tcl.ai.note.handwritingtext.ui.richtext.RichTextController;
import com.tcl.ai.note.utils.CommonUtilsKt;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextKTUtilsKt;
import com.tcl.ai.note.handwritingtext.ui.richtext.base.BaseRichTextEditView;
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import android.text.InputFilter;
import android.text.Spanned;

import androidx.core.content.ContextCompat;
import com.tcl.ai.note.base.R;

/**
 * All Rights Reserved.
 * <AUTHOR> Liu
 * Android RichEditor EditText
 * 扩展自 AppCompatEditText，用于实现富文本输入与样式控制。
 * 支持粗体、斜体、下划线、对齐、列表、待办事项等多种格式。

 * 具备：
 * - 富文本样式列表管理
 * - 样式Status监听回调
 * - 对齐状态同步Compose/工具栏选中
 * - 富文本内容与html互转
 * - 监控输入变化与光标变化，自动同步按钮激活状态等
 *
 */
public class AREditText extends BaseRichTextEditView {

	// 日志打印开关
	private static boolean LOG = true;

	// 富文本内部状态监控开关
	private boolean APPLY_MONITORING = true;
	private boolean STORAGE_MONITORING = true;
	private boolean UISHOW_MONITORING = true;

	// 修正光标功能相关字段
	private boolean mShouldFixCursor = false;
	private int mSelection;

	// 样式对象集合，存储所有已注册的样式
	private final List<IARE_Style> sStylesList = new ArrayList<>();

	// 上下文对象
	private Context mContext;

	// 关联的Note对象
	private Note mNote;

	// 文本输入监听器
	private TextWatcher mTextWatcher;

	// 行距
	private float mLineSpace;

	// 保存内容到内存的任务Runnable
	private Runnable mSaveContentToMemoryTask;

	// 样式变更监听器(由RichTextViewHolder绑定)
	protected StyleStatusListener mStyleStatusListener;

	// 添加历史记录管理器
	private final RichTextUndoRedoManager mRichTextUndoRedoManager;

	// 撤销/重做状态监听器
	private UndoRedoStateListener mUndoRedoStateListener;

	// 复制监听
	private OnCopyListener mOnCopyListener;
	private boolean  mPreview;

	private boolean isDestroyed = false;

	// 是否输入回车换行符
	private boolean isEnter = false;

	// 富文本最大输入长度
	private int maxContentLength = RichTextController.MAX_CONTENT_LENGTH;

	// 保存最后删除文字前的样式状态
	private StyleState mLastDeletedStyleState = null;
	// 超限时回调（toast触发）
	private OnLimitListener onLimitListener;

	private int resetPasteStart = -1;
	private int resetPasteCount = 0;
	private  boolean isPasting = false;
	// 是否重置了粘贴，防止取消粘贴后，内容被粘贴进去
	private  boolean isResetPasteSuccess = false;

	// 跳过列表样式应用
	private boolean skipNextListApply = false;


	// 忽略selectionChanged事件
	private boolean ignoreSelectionChanged = false;
	/**
	 * 忽略selectionChanged事件
	 * @param ignore
	 */
	public void setIgnoreSelectionChanged(boolean ignore) {
		ignoreSelectionChanged = ignore;
	}

	private final Paint mPrefixBgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);


	/**
	 * 样式状态保存类
	 */
	private static class StyleState {
		boolean bold = false;
		boolean italic = false;
		boolean underline = false;
		boolean strikethrough = false;
		Integer fontColor = null;
		Integer bgColor = null;
		Integer fontSize = null;

		StyleState(boolean bold, boolean italic, boolean underline, boolean strikethrough,
				   Integer fontColor, Integer bgColor, Integer fontSize) {
			this.bold = bold;
			this.italic = italic;
			this.underline = underline;
			this.strikethrough = strikethrough;
			this.fontColor = fontColor;
			this.bgColor = bgColor;
			this.fontSize = fontSize;
		}
	}

	/**
	 * 获取所有已注册的样式列表
	 * @return
	 */
	public List<IARE_Style> getStylesList() {
		return sStylesList;
	}

	/**
	 * 保存当前位置的样式状态
	 * @param pos 要检查的位置
	 */
	private void saveCurrentStyleState(int pos) {
		Editable editable = getEditableText();
		if (editable == null || pos < 0 || pos >= editable.length()) {
			return;
		}

		// 获取当前位置的样式状态
		boolean bold = hasCharStyleOnPos(editable, pos, android.text.style.StyleSpan.class, android.graphics.Typeface.BOLD);
		boolean italic = hasCharStyleOnPos(editable, pos, android.text.style.StyleSpan.class, android.graphics.Typeface.ITALIC);
		boolean underline = hasCharStyleOnPos(editable, pos, AreUnderlineSpan.class, null);
		boolean strikethrough = hasCharStyleOnPos(editable, pos, StrikethroughSpan.class, null);
		Integer fontColor = getColorOnPos(editable, pos);
		Integer bgColor = getBgColorOnPos(editable, pos);
		Integer fontSize = getFontSizeOnPos(editable, pos);

		mLastDeletedStyleState = new StyleState(bold, italic, underline, strikethrough, fontColor, bgColor, fontSize);
	}

	/*
	 * 清空最后删除的样式状态
	 */
	public void clearLastDeletedStyleState() {
		mLastDeletedStyleState = null;
	}

	/**
	 * 检查当前行是否为空（没有实际文字内容）
	 * @param cursorPosition 光标位置
	 * @return true 如果当前行为空或只包含零宽字符
	 */
	private boolean isCurrentLineEmpty(int cursorPosition) {
		try {
			Editable editable = getEditableText();
			if (editable == null || editable.length() == 0) {
				return true;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return true;
			}

			int line = layout.getLineForOffset(cursorPosition);
			int lineStart = layout.getLineStart(line);
			int lineEnd = layout.getLineEnd(line);

			// 如果行结束位置有换行符，不包括换行符
			if (lineEnd > lineStart && lineEnd <= editable.length() && editable.charAt(lineEnd - 1) == '\n') {
				lineEnd--;
			}

			// 如果行为空或只有换行符，认为行为空
			if (lineEnd <= lineStart) {
				return true;
			}

			// 获取该行的内容
			CharSequence lineContent = editable.subSequence(lineStart, lineEnd);

			// 检查行内容是否只包含零宽字符
			for (int i = 0; i < lineContent.length(); i++) {
				char c = lineContent.charAt(i);
				// 如果发现非零宽字符的内容，说明有实际文字
				if (c != Constants.ZERO_WIDTH_SPACE_INT) {
					return false;
				}
			}

			// 如果只包含零宽字符或为空，则认为行为空
			return true;
		} catch (Exception e) {
			Logger.e("AREditText", "Error checking if line is empty: " + e.getMessage());
			// 出错时保守处理，认为行不为空
			return false;
		}
	}

	/**
	 * 同步样式对象状态与保存的样式状态
	 */
	private void syncStyleObjectsWithSavedState() {
		if (mLastDeletedStyleState == null || sStylesList == null) {
			return;
		}

		for (IARE_Style style : sStylesList) {
			if (style instanceof ARE_Bold) {
				style.updateCheckStatus(mLastDeletedStyleState.bold);
			} else if (style instanceof ARE_Italic) {
				style.updateCheckStatus(mLastDeletedStyleState.italic);
			} else if (style instanceof ARE_Underline) {
				style.updateCheckStatus(mLastDeletedStyleState.underline);
			} else if (style instanceof ARE_Strikethrough) {
				style.updateCheckStatus(mLastDeletedStyleState.strikethrough);
			} else if (style instanceof ARE_FontColor) {
				style.updateCheckStatus(mLastDeletedStyleState.fontColor != null);
			} else if (style instanceof ARE_BackgroundColor) {
				style.updateCheckStatus(mLastDeletedStyleState.bgColor != null);
			} else if (style instanceof ARE_FontSize) {
				style.updateCheckStatus(mLastDeletedStyleState.fontSize != null);
			}
		}
	}

	/**
	 * 自动应用当前激活的字符样式到新输入的文字
	 * @param editable 可编辑文本
	 * @param start 新输入文字的开始位置
	 * @param end 新输入文字的结束位置
	 */
	private void applyActiveCharacterStylesToNewInput(Editable editable, int start, int end) {
		if (sStylesList == null || start >= end) {
			return;
		}

		// 遍历所有字符样式，将激活的样式应用到新输入的文字
		for (IARE_Style style : sStylesList) {
			// 只处理字符级别的样式（粗体、斜体、下划线、删除线、字体颜色、背景色、字号）
			if (style instanceof ARE_Bold || style instanceof ARE_Italic ||
					style instanceof ARE_Underline || style instanceof ARE_Strikethrough ||
					style instanceof ARE_FontColor || style instanceof ARE_BackgroundColor ||
					style instanceof ARE_FontSize) {

				// 如果样式处于激活状态，应用到新输入的文字
				if (style.getIsChecked()) {
					try {
						// 临时关闭监控，避免递归调用
						boolean wasMonitoring = APPLY_MONITORING;
						APPLY_MONITORING = false;

						// 应用样式到新输入的文字范围
						style.applyStyle(editable, start, end, false);

						// 恢复监控状态
						APPLY_MONITORING = wasMonitoring;
					} catch (Exception e) {
						Logger.e("AREditText", "Error applying style to new input: " + e.getMessage());
					}
				}
			}
		}
	}
	/**
	 * 设置最大长度同时应用过滤器
	 * @param max
	 */
	public void setMaxContentLength(int max) {
		this.maxContentLength = max;
		// 每次变化都要重新应用过滤器
		applyMaxLengthFilter();
	}
	// 设置内容长度超限监听器
	public void setOnLimitListener(OnLimitListener l) { this.onLimitListener = l; }

	// 超限监听接口（toast提示栏可用）
	public interface OnLimitListener {
		void onInputLimit(); // 输入被拦截
		void onPasteLimit(); // 粘贴被拦截
	}

	/**
	 * 检查是否已销毁
	 */
	public boolean isDestroyed() {
		return isDestroyed;
	}

	// 构造方法
	public AREditText(Context context) {
		this(context, null);
	}
	public AREditText(Context context, AttributeSet attrs) {
		this(context, attrs, 0);
	}
	public AREditText(Context context, AttributeSet attrs, int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		mContext = context;
//		initGlobalValues();
//		init();
		// 注册输入相关监听(样式、监控等)
		setupListener();
		mLineSpace = getLineSpacingExtra();
		this.setMovementMethod(EditMovementMethod.getInstance());
		//disable framework textview suggestion for monkey test
		setInputType(getInputType() | InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS);

		// 初始化历史记录管理器
		mRichTextUndoRedoManager = new RichTextUndoRedoManager(this);

		applyMaxLengthFilter();

		initPrefixBgPaint();
	}

	/**
	 * 初始化前缀背景画笔
	 */
	private void initPrefixBgPaint() {
		mPrefixBgPaint.setStyle(Paint.Style.FILL);
		// 使用 getContext() 更保险
		try {
			int color = ContextCompat.getColor(getContext(), com.tcl.ai.note.base.R.color.text_field_border);
			mPrefixBgPaint.setColor(color);
		} catch (Exception e) {
			// 回退色，避免崩溃
			mPrefixBgPaint.setColor(0xFF4968EE);
		}
	}

	// 初始化屏幕尺寸全局值
	private void initGlobalValues() {
//		int[] wh = Util.getScreenWidthAndHeight(mContext);
//		Constants.SCREEN_WIDTH = wh[0];
//		Constants.SCREEN_HEIGHT = wh[1];
	}

	// 设置UI属性，默认为白底、多行编辑
	private void init() {
		// this.setMovementMethod(new AREMovementMethod());
		this.setFocusableInTouchMode(true);
		this.setBackgroundColor(Color.WHITE);
		this.setInputType(EditorInfo.TYPE_CLASS_TEXT | EditorInfo.TYPE_TEXT_FLAG_MULTI_LINE
				| EditorInfo.TYPE_TEXT_FLAG_NO_SUGGESTIONS);
		int padding = 8;
		padding = Util.getPixelByDp(mContext, padding);
		this.setPadding(padding, padding, padding, padding);
		this.setTextSize(TypedValue.COMPLEX_UNIT_SP, Constants.DEFAULT_FONT_SIZE);
	}

	/**
	 * 设置富文本样式变化Status监听器（RichTextViewHolder实现并注入，用于回报UI/状态管理/Compose）
	 * @param listener
	 */
	public void setStyleStatusListener(StyleStatusListener listener) {
		this.mStyleStatusListener = listener;
	}

	/** 注册样式（如粗体/斜体/对齐...）
	 * 设置后，输入内容时会自动生效并调用监听器回调
	 */
	public void setInStylesList(IARE_Style style) {
		style.setEditText(this);
		style.setisValid(true);
		sStylesList.add(style);
	}

	/** 清空所有样式注册 */
	public void clearStylesList(){
		for(IARE_Style style : sStylesList) {
			style.updateCheckStatus(false);
			style.setisValid(false);
			style.setEditText(null);
		}
		sStylesList.clear();
	}

	// 内容变化监听
	private OnContentChangedListener onContentChangedListener;
	public interface OnContentChangedListener {
		void onContentChanged(CharSequence text);
	}

	// 用于记录删除前的列表span信息
	private java.util.List<Object> spansToRemoveAfterDelete = new java.util.ArrayList<>();
	public void setOnContentChangedListener(OnContentChangedListener listener) {
		this.onContentChangedListener = listener;
	}

	public void setOnCopyListener(OnCopyListener listener) {
		this.mOnCopyListener = listener;
	}

	public void saveContent(String content) {
		if (onContentChangedListener != null) {
			onContentChangedListener.onContentChanged(content);
		}
	}

	@Override
	public boolean addNewLineByPoint(PointF point) {
		skipNextListApply = true;
		boolean result = super.addNewLineByPoint(point); // 仍然调用父类逻辑
		skipNextListApply = false;
		return result;
	}

	@Override
	protected void onDraw(Canvas canvas) {
		// 先绘制自定义的前缀背景（作为底层）
		drawSelectedListPrefixBackgrounds(canvas);
		// 然后让系统绘制文本、图标等（在上层）
		super.onDraw(canvas);
	}

	/**
	 * 拦截超长输入和超长粘贴的InputFilter
	 */
	private final InputFilter maxLengthFilter = new InputFilter() {
		@Override
		public CharSequence filter(CharSequence source, int start, int end,
								   Spanned dest, int dstart, int dend) {
			// dest: 旧有内容，source: 新输内容，dstart/dend：要替换的区间
			// 剩余可插入字数
			int keep = maxContentLength - (dest.length() - (dend - dstart));
			if (keep <= 0) {
				// 已达到最大限制，拒绝输入
				if (onLimitListener != null) onLimitListener.onInputLimit();
				return ""; // 拦截
			} else if (keep >= end - start) {
				// 新内容可以完全写进
				return null;
			} else {
				// 只能截取局部，新内容部分能进来，加提示
				if (onLimitListener != null) onLimitListener.onInputLimit();
				return source.subSequence(start, start + keep);
			}
		}
	};

	/**
	 * 应用/刷新最大长度InputFilter，避免多个filter重复设置
	 */
	private void applyMaxLengthFilter() {
		InputFilter[] old = getFilters();
		if (old == null) {
			setFilters(new InputFilter[]{maxLengthFilter});
		} else {
			ArrayList<InputFilter> newf = new ArrayList<>();
			boolean has = false;
			for (InputFilter f : old) {
				if (f == maxLengthFilter) has = true;
				newf.add(f);
			}
			if (!has) newf.add(maxLengthFilter);
			setFilters(newf.toArray(new InputFilter[0]));
		}
	}

	/**
	 * 添加输入/文本的核心监听（setupListener内部调用）
	 */
	private void setupListener() {
		setupTextWatcher();
	} // #End of setupListener()

	/**
	 * 文本变化监听＋删除监听
	 */
	private void setupTextWatcher() {
		mTextWatcher = new TextWatcher() {

			int startPos = 0;
			int endPos = 0;
			int before = 0;
			int count = 0;
			boolean isDelete;
			// 是否存在自定义格式数据
			boolean isPasteCustomStyle;


			@Override
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {
				if (!APPLY_MONITORING) {
					return;
				}
				if (LOG) {
					Logger.d("beforeTextChanged:: s = " + s + "---size=" + AREditText.this.getEditableText().length() +" start = " + start + "---count = " + count
							+ ", after = " + after);
				}

				// 清空之前记录的span信息
				spansToRemoveAfterDelete.clear();

				// 如果是删除操作（count > 0 且 after == 0），保存删除前的样式状态和列表span信息
				if (count > 0 && after == 0) {
					// 总是保存被删除文字的样式状态（删除范围内最后一个字符的样式）
					// 这样可以确保保存的是当前行最后一个被删除字符的实际样式
					int savePos = Math.max(0, start + count - 1);
					if (savePos < s.length()) {
						saveCurrentStyleState(savePos);
					}

					// 分析删除范围，确定需要删除哪些列表span
					analyzeDeleteRangeAndRecordSpans(s, start, count);
				}
			}

			@Override
			public void onTextChanged(CharSequence s, int start, int before, int count) {
				if (!APPLY_MONITORING) {
					return;
				}

				if (LOG) {
					Logger.d("onTextChanged:: s = " + s + "---size=" + AREditText.this.getEditableText().length() +" start = " + start + "---count = " + count + "---before = "
							+ before);
				}
				this.startPos = start;
				this.endPos = start + count;
				this.before = before;
				this.count = count;
				// 记录文本变化操作
				isDelete = before > 0 && count == 0;
				if (before >= 0 && count > 0) {
					// 新增字符
					String inputText = s.toString().substring(start, start + count);
					isPasteCustomStyle = inputText.contains(RichTextStyleEntityToSpanConverter.TODO_FLAG)
							|| inputText.contains(RichTextStyleEntityToSpanConverter.TODO_FLAG_CHECK)
							|| inputText.contains(RichTextStyleEntityToSpanConverter.BULLET_FLAG)
							|| containListNumber(inputText);
					// 输入换行符
					if (("\n").equals(inputText)) {
						isEnter = true;
					}
				}
			}

			@Override
			public void afterTextChanged(Editable s) {
				if (!APPLY_MONITORING) {
					return;
				}

				if (LOG) {
					Logger.d("afterTextChanged:: s = " + s + "---size=" + AREditText.this.getEditableText().length() +" startPos=" + startPos + "---endPos=" + endPos);
				}

				if (endPos <= startPos) {
					Logger.d("User deletes: start == " + startPos + "---endPos == " + endPos);
				}

				// 只有输入回车换行符时处理待办、段落、数字这三个样式的刷新
				if(isEnter && !skipNextListApply){
					isEnter = false;
					// 回调所有样式applyStyle方法，实时处理输入
					for (IARE_Style style : sStylesList) {
						if (style.needApplyStyle()) {
							style.applyStyle(s, startPos, endPos, false);
						}
					}
				}

				// 处理字符样式的自动应用（粗体、斜体、下划线等）
				if (endPos > startPos && !isDelete && !isPasteCustomStyle && !isEnter) {
					// 如果有保存的样式状态，先同步样式对象状态
					if (mLastDeletedStyleState != null) {
						syncStyleObjectsWithSavedState();
					}

					// 自动应用当前激活的字符样式到新输入的文字
					applyActiveCharacterStylesToNewInput(s, startPos, endPos);
				}

				// 处理删除操作，特别是对列表和待办事项的处理
				if (isDelete && !spansToRemoveAfterDelete.isEmpty()) {
					// 处理在beforeTextChanged中记录的需要删除的列表span
					boolean hasNumberSpansDeleted = false;
					Object remainingListSpan = null; // 保留的列表span（开始位置的span）

					// 收集需要清理的零宽字符位置
					java.util.List<Integer> zeroWidthPositions = new java.util.ArrayList<>();

					// 检查是否是全选删除（删除起始位置为0且删除后文本为空或接近为空）
					boolean isSelectAllDelete = (startPos == 0) && (s.length() <= 1);

					// 只有在非全选删除的情况下才考虑保留开始位置的列表span
					if (!isSelectAllDelete) {
						Layout layout = getLayout();
						if (layout != null) {
							int startLine = layout.getLineForOffset(startPos);
							int lineStart = layout.getLineStart(startLine);
							int lineEnd = layout.getLineEnd(startLine);

							// 检查是否是从行首删除（可能导致内容合并到前一行）
							boolean isDeleteFromLineStart = (startPos == lineStart);

							Object targetSpan = null;

							if (isDeleteFromLineStart && startLine > 0) {
								// 从行首删除，内容会合并到前一行，应该保留前一行的span
								int prevLine = startLine - 1;
								int prevLineStart = layout.getLineStart(prevLine);
								int prevLineEnd = layout.getLineEnd(prevLine);

								// 查找前一行的列表span
								UpcomingListSpan[] prevUpcomingSpans = s.getSpans(prevLineStart, prevLineEnd, UpcomingListSpan.class);
								if (prevUpcomingSpans != null && prevUpcomingSpans.length > 0) {
									targetSpan = prevUpcomingSpans[0];
								} else {
									ListBulletSpan[] prevBulletSpans = s.getSpans(prevLineStart, prevLineEnd, ListBulletSpan.class);
									if (prevBulletSpans != null && prevBulletSpans.length > 0) {
										targetSpan = prevBulletSpans[0];
									} else {
										ListNumberSpan[] prevNumberSpans = s.getSpans(prevLineStart, prevLineEnd, ListNumberSpan.class);
										if (prevNumberSpans != null && prevNumberSpans.length > 0) {
											targetSpan = prevNumberSpans[0];
										}
									}
								}
							}

							// 如果没有找到前一行的span，或者不是从行首删除，则查找当前行的span
							if (targetSpan == null) {
								UpcomingListSpan[] upcomingSpans = s.getSpans(lineStart, lineEnd, UpcomingListSpan.class);
								if (upcomingSpans != null && upcomingSpans.length > 0) {
									targetSpan = upcomingSpans[0];
								} else {
									ListBulletSpan[] bulletSpans = s.getSpans(lineStart, lineEnd, ListBulletSpan.class);
									if (bulletSpans != null && bulletSpans.length > 0) {
										targetSpan = bulletSpans[0];
									} else {
										ListNumberSpan[] numberSpans = s.getSpans(lineStart, lineEnd, ListNumberSpan.class);
										if (numberSpans != null && numberSpans.length > 0) {
											targetSpan = numberSpans[0];
										}
									}
								}
							}

							// 只有当找到的span不在删除列表中时，才保留它
							if (targetSpan != null && !spansToRemoveAfterDelete.contains(targetSpan)) {
								remainingListSpan = targetSpan;
							}
						}
					}

					for (Object spanObj : spansToRemoveAfterDelete) {
						// 如果是开始位置的span，不删除，后续需要扩展其范围
						if (spanObj == remainingListSpan) {
							continue;
						}

						if (spanObj instanceof UpcomingListSpan) {
							UpcomingListSpan span = (UpcomingListSpan) spanObj;
							// 检查span是否还存在于文本中
							int spanStart = s.getSpanStart(span);
							int spanEnd = s.getSpanEnd(span);
							if (spanStart >= 0 && spanEnd >= 0) {
								// 删除span
								s.removeSpan(span);
								// 记录零宽字符位置
								if (spanStart < s.length() && s.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT) {
									zeroWidthPositions.add(spanStart);
								}
							}
						} else if (spanObj instanceof ListBulletSpan) {
							ListBulletSpan span = (ListBulletSpan) spanObj;
							// 检查span是否还存在于文本中
							int spanStart = s.getSpanStart(span);
							int spanEnd = s.getSpanEnd(span);
							if (spanStart >= 0 && spanEnd >= 0) {
								// 删除span
								s.removeSpan(span);
								// 记录零宽字符位置
								if (spanStart < s.length() && s.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT) {
									zeroWidthPositions.add(spanStart);
								}
							}
						} else if (spanObj instanceof ListNumberSpan) {
							ListNumberSpan span = (ListNumberSpan) spanObj;
							// 检查span是否还存在于文本中
							int spanStart = s.getSpanStart(span);
							int spanEnd = s.getSpanEnd(span);
							if (spanStart >= 0 && spanEnd >= 0) {
								// 删除span
								s.removeSpan(span);
								// 记录零宽字符位置
								if (spanStart < s.length() && s.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT) {
									zeroWidthPositions.add(spanStart);
								}
								hasNumberSpansDeleted = true;
							}
						}
					}

					// 从后往前删除零宽字符，避免位置偏移问题
					java.util.Collections.sort(zeroWidthPositions, java.util.Collections.reverseOrder());
					for (Integer pos : zeroWidthPositions) {
						if (pos < s.length() && s.charAt(pos) == Constants.ZERO_WIDTH_SPACE_INT) {
							s.delete(pos, pos + 1);
						}
					}

					// 扩展保留的列表span范围以包含合并的内容
					if (remainingListSpan != null) {
						expandListSpanToIncludeMergedContent(s, remainingListSpan, startPos);
					}

					// 强制修复所有列表span范围
					post(new Runnable() {
						@Override
						public void run() {
							fixAllListSpanRangesAfterDelete();
						}
					});

					// 清空记录的span信息
					spansToRemoveAfterDelete.clear();

					// 如果删除了有序列表项，需要重新编号剩余的列表项
					if (hasNumberSpansDeleted) {
						post(new Runnable() {
							@Override
							public void run() {
								for (IARE_Style style : sStylesList) {
									if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
										((ARE_ListNumber) style).reNumberAllListItemsPublic(s);
										break;
									}
								}
							}
						});
					} else {
						// 额外检查：即使没有预记录的span删除，也检查是否需要重新编号
						// 这种情况可能发生在用户通过其他方式删除了有序列表项
						ListNumberSpan[] remainingSpans = s.getSpans(0, s.length(), ListNumberSpan.class);
						if (remainingSpans != null && remainingSpans.length > 0) {
							// 检查编号是否连续，如果不连续则重新编号
							boolean needsRenumbering = checkIfRenumberingNeeded(s, remainingSpans);
							if (needsRenumbering) {
								post(new Runnable() {
									@Override
									public void run() {
										for (IARE_Style style : sStylesList) {
											if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
												((ARE_ListNumber) style).reNumberAllListItemsPublic(s);
												break;
											}
										}
									}
								});
							}
						}
					}
				} else if (isDelete) {
					// 处理没有预记录span的删除情况（保持原有逻辑）
					int deleteStart = startPos;
					int deleteEnd = startPos + before;

					// 处理待办事项删除
					UpcomingListSpan[] upcomingSpans = s.getSpans(deleteStart, deleteStart, UpcomingListSpan.class);
					if (upcomingSpans != null && upcomingSpans.length > 0) {
						for (IARE_Style style : sStylesList) {
							if (style instanceof ARE_Upcoming && style.needApplyStyle()) {
								style.applyStyle(s, deleteStart, deleteStart, false);
								break;
							}
						}
					}

					// 处理无序列表删除
					ListBulletSpan[] bulletSpans = s.getSpans(deleteStart, deleteStart, ListBulletSpan.class);
					if (bulletSpans != null && bulletSpans.length > 0) {
						for (IARE_Style style : sStylesList) {
							if (style instanceof ARE_ListBullet && style.needApplyStyle()) {
								style.applyStyle(s, deleteStart, deleteStart, false);
								break;
							}
						}
					}

					// 处理有序列表删除
					ListNumberSpan[] numberSpans = s.getSpans(deleteStart, deleteStart, ListNumberSpan.class);
					if (numberSpans != null && numberSpans.length > 0) {
						for (IARE_Style style : sStylesList) {
							if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
								style.applyStyle(s, deleteStart, deleteStart, false);
								break;
							}
						}
					} else {
						// 如果删除位置没有列表项，但文档中还有其他列表项，则重新编号
						ListNumberSpan[] allListSpans = s.getSpans(0, s.length(), ListNumberSpan.class);
						if (allListSpans != null && allListSpans.length > 0) {
							// 延迟执行重新编号，避免与其他删除处理冲突
							post(new Runnable() {
								@Override
								public void run() {
									for (IARE_Style style : sStylesList) {
										if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
											((ARE_ListNumber) style).reNumberAllListItemsPublic(s);
											break;
										}
									}
								}
							});
						}
					}
				}
				// 存在自定义格式数据，不记录撤销/重做操作， 异步处理完自定格式数据后再记录撤销/重做操作
				if (!isPasteCustomStyle) {
					RichTextKTUtilsKt.recordDeleteStyleToUndoRedo(startPos, before, count, isDelete, mRichTextUndoRedoManager);

					if (onContentChangedListener != null) {
						onContentChangedListener.onContentChanged(s);
					}
				}
				isPasting = false;
				// 处理粘贴的自定义格式数据
				if (isPasteCustomStyle) {
					isResetPasteSuccess = false;
					isPasting = true;
					resetPasteStart = startPos;
					resetPasteCount = count;
					stopApplyMonitor();
					Logger.d("User inserts: start == " + startPos + "---endPos == " + endPos);
					isPasteCustomStyle = false;
					mOnCopyListener.startPaste(s, startPos, endPos, before, count, isDelete);
				}
			}
		};

		this.addTextChangedListener(mTextWatcher);

		// 监听物理键盘删除事件，适配部分边界场景
		this.setOnKeyListener(new OnKeyListener() {
			@Override
			public boolean onKey(View v, int keyCode, KeyEvent event) {
				if (keyCode == KeyEvent.KEYCODE_DEL && event.getAction() == KeyEvent.ACTION_DOWN) {
					int selctionStart = AREditText.this.getSelectionStart();
					Layout layout = AREditText.this.getLayout();
					int line = layout.getLineForOffset(selctionStart);
					int lineStart = layout.getLineStart(line);

					// 删除整行的富文本样式 - 修改条件：当光标在行首或行内只有列表前缀字符时，且该行有列表项span时就可以删除前缀符号
					boolean canDeletePrefix = false;
					if (hasListSpanOnCurrentLine(selctionStart)) {
						// 只有在该列表项的首行行首，才满足删除前缀条件
						if ((selctionStart == lineStart && isCursorAtListBlockFirstLine(getEditableText(), selctionStart))
								|| selctionStart == 0) {
							canDeletePrefix = true;
						} else {
							// 检查光标前是否只有零宽字符（列表前缀）
							canDeletePrefix = isOnlyZeroWidthCharsBefore(selctionStart, lineStart);
						}
					}

					if (canDeletePrefix) {
						if (selctionStart == 0) {
							// 解决删除第一个位置的待办后，重新添加待办失效问题
							RichTextEventManager.INSTANCE.clearStyleEvent();
						}

						// 检查是否会有内容合并到前一行
						boolean willMergeContent = checkIfContentWillMerge(selctionStart);
						Object targetListSpan = null;
						if (willMergeContent) {
							targetListSpan = findTargetListSpanForMerge(selctionStart);
						}


						for (IARE_Style style : sStylesList) {
							if (style.needApplyStyle()) {
								style.removeStyle(AREditText.this.getEditableText(), selctionStart, selctionStart);
							}
						}

						// 扩展目标span范围或执行通用修复
						if (willMergeContent && targetListSpan != null) {
							final Object finalTargetSpan = targetListSpan;
							post(new Runnable() {
								@Override
								public void run() {
									expandListSpanAfterMerge(AREditText.this.getEditableText(), finalTargetSpan);
								}
							});
						} else {
							post(new Runnable() {
								@Override
								public void run() {
									fixAllListSpanRangesAfterDelete();
								}
							});
						}

						// 延迟再次修复
						postDelayed(new Runnable() {
							@Override
							public void run() {
								fixAllListSpanRangesAfterDelete();
							}
						}, 100);

						// 检查是否需要重新编号有序列表
						post(new Runnable() {
							@Override
							public void run() {
								Editable editable = AREditText.this.getEditableText();
								ListNumberSpan[] listSpans = editable.getSpans(0, editable.length(), ListNumberSpan.class);
								if (listSpans != null && listSpans.length > 0) {
									for (IARE_Style style : sStylesList) {
										if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
											((ARE_ListNumber) style).reNumberAllListItemsPublic(editable);
											break;
										}
									}
								}
							}
						});

						// 删除前缀后，手动触发样式状态更新，确保工具栏状态同步
						post(new Runnable() {
							@Override
							public void run() {
								// 手动触发onSelectionChanged来更新样式状态
								int currentSelection = AREditText.this.getSelectionStart();
								AREditText.this.onSelectionChanged(currentSelection, currentSelection);
							}
						});
					} else {
						// 处理删除换行符/合并行的情况
						Editable editable = AREditText.this.getEditableText();
						if (editable != null && selctionStart > 0) {
							// 判断删除是否会导致与上一行合并
							if (checkIfContentWillMerge(selctionStart)) {
								Object targetListSpan = findTargetListSpanForMerge(selctionStart);
								if (targetListSpan != null) {
									final Object finalTargetSpan = targetListSpan;
									postDelayed(new Runnable() {
										@Override
										public void run() {
											expandListSpanAfterMerge(AREditText.this.getEditableText(), finalTargetSpan);
										}
									}, 50);
								}
							}
						}

						// 通用修复作为保险
						postDelayed(new Runnable() {
							@Override
							public void run() {
								fixAllListSpanRangesAfterDelete();
							}
						}, 100);
					}
				}
				return false;
			}
		});
	}


	/**
	 * 检查当前行是否有列表项span（待办、有序列表、无序列表）
	 * @param cursorPosition 当前光标位置
	 * @return true 如果该行有列表项span，false 如果没有
	 */
	private boolean hasListSpanOnCurrentLine(int cursorPosition) {
		try {
			Editable editable = getEditableText();
			if (editable == null || editable.length() == 0) {
				return false;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return false;
			}

			int line = layout.getLineForOffset(cursorPosition);
			int lineStart = layout.getLineStart(line);
			int lineEnd = layout.getLineEnd(line);

			// 检查该行是否有列表项span（待办、有序列表、无序列表）
			UpcomingListSpan[] upcomingSpans = editable.getSpans(lineStart, lineEnd, UpcomingListSpan.class);
			ListNumberSpan[] numberSpans = editable.getSpans(lineStart, lineEnd, ListNumberSpan.class);
			ListBulletSpan[] bulletSpans = editable.getSpans(lineStart, lineEnd, ListBulletSpan.class);

			return (upcomingSpans != null && upcomingSpans.length > 0) ||
					(numberSpans != null && numberSpans.length > 0) ||
					(bulletSpans != null && bulletSpans.length > 0);
		} catch (Exception e) {
			Logger.e("AREditText", "Error checking list span on current line: " + e.getMessage());
			// 出错时保守处理，不删除前缀符号
			return false;
		}
	}

	/**
	 * 判断光标前是否只有零宽字符（并且在列表块的首行）
	 * 多行情况下，非首行永远返回 false
	 *
	 * @param position 光标位置
	 * @param lineStart 当前行的起始位置
	 * @return true 光标前只有 ZWS 且为块首行
	 */
	private boolean isOnlyZeroWidthCharsBefore(int position, int lineStart) {
		Editable editable = getEditableText();
		if (editable == null) return false;

		// 如果不是当前列表块的首行，直接 false
		if (!isCursorAtListBlockFirstLine(editable, position)) {
			return false;
		}

		// 检查 lineStart ~ position 之间是否只有 ZWS
		for (int i = lineStart; i < position; i++) {
			if (editable.charAt(i) != Constants.ZERO_WIDTH_SPACE_INT) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 判断光标是否位于当前列表块的首行
	 *
	 * @param editable 当前 Editable
	 * @param position 光标位置
	 * @return true 表示光标在列表块的首行
	 */
	private boolean isCursorAtListBlockFirstLine(Editable editable, int position) {
		Layout layout = getLayout();
		if (layout == null) return false;

		// 找光标位置所在的列表 span
		Object[] spans = getListSpansInRange(editable, position, position);
		if (spans.length == 0) return false;

		Object span = spans[0];
		int spanStart = editable.getSpanStart(span);

		// 列表块的第一行起始位置
		int firstLineOfSpan = layout.getLineForOffset(spanStart);
		int currentLine     = layout.getLineForOffset(position);

		return currentLine == firstLineOfSpan;
	}

	public void resetPaste() {
		if (isPasting) {
			try {
				stopApplyMonitor();
				if (resetPasteStart != -1 && resetPasteCount > 0) {
					getEditableText().delete(resetPasteStart, resetPasteStart + resetPasteCount);
				}
				isResetPasteSuccess = true;
			} catch (Exception e) {
				Logger.e("AREditText", "Error reset paste: " + e.getMessage());
			} finally {
				resetPasteStart = -1;
				resetPasteCount = 0;
				isPasting = false;
				startApplyMonitor();
			}
		}
	}

	public void handlePaste(Editable editable, int start, int end, int before, int count, boolean isDelete) {
		try {
			Layout layout = this.getLayout();
			SpannableStringBuilder builder = new SpannableStringBuilder(editable);
			String text = builder.toString();
			Logger.d("RichTextViewModel2", "Text length=" + text.length() + "  " + text);
			List<StyleRange> styleRanges = new ArrayList<>();
			// 查找待办样式
			findCustomTag(text, styleRanges, RichTextStyleEntityToSpanConverter.TODO_FLAG, layout, "todo", false);
			findCustomTag(text, styleRanges, RichTextStyleEntityToSpanConverter.TODO_FLAG_CHECK, layout, "todo", true);
			// 查找段落样式
			findCustomTag(text, styleRanges, RichTextStyleEntityToSpanConverter.BULLET_FLAG, layout, "bullet", false);
			// 查找数字样式
			int startNumber = findCustomNumberTag(text, styleRanges, layout);

			// 替换自定义标识, 同时更新样式起始位置
			int replaceCount = 0;
			replaceCount += replace(builder, RichTextStyleEntityToSpanConverter.TODO_FLAG, styleRanges);
			replaceCount += replace(builder, RichTextStyleEntityToSpanConverter.TODO_FLAG_CHECK, styleRanges);
			replaceCount += replace(builder, RichTextStyleEntityToSpanConverter.BULLET_FLAG, styleRanges);
			replaceCount += replaceNumber(builder, startNumber, styleRanges);
			int finalReplaceCount = replaceCount;

			RichTextStyleEntityToSpanConverter.INSTANCE.applyPasteToRichText(styleRanges, builder, layout);
			// 有序列表需要重新排序
			for (IARE_Style style : sStylesList) {
				if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
					((ARE_ListNumber) style).reNumberAllListItemsPublicNoRefresh(builder);
					break;
				}
			}

			Logger.d("RichTextViewModel2", "paste 11");
			// 保存当前滚动位置，防止setText时滚动到顶部
			saveScrollPosition();

			if (!isResetPasteSuccess) {
				post(new Runnable() {
					@Override
					public void run() {
						Logger.d("RichTextViewModel2", "paste 22");
						setText(builder);
						setSelection(min(end - finalReplaceCount, builder.length()));

//						RichTextKTUtilsKt.recordDeleteStyleToUndoRedo(start, before, count - finalReplaceCount, isDelete, mRichTextUndoRedoManager);
						startApplyMonitor();

						// 恢复正常滚动行为
						restoreScrollBehavior();

						if (onContentChangedListener != null) {
							onContentChangedListener.onContentChanged(builder);
						}
					}
				});
			} else {
				// 即使重置粘贴成功，也要恢复滚动行为
				restoreScrollBehavior();
			}
		} catch (Exception e) {
			Logger.e("AREditText", "Error handle paste: " + e.getMessage());
			// 出错时也要恢复正常滚动行为
			restoreScrollBehavior();
		} finally {
			isPasting = false;
			startApplyMonitor();
		}
	}

	private void findCustomTag(String text, List < StyleRange> styleRanges, String tag, Layout layout, String styleValue, boolean isCheck) {
		int index = -1;
		while((index = text.indexOf(tag, index +1)) != -1){
			int lineNumber = layout.getLineForOffset(index);
			int lineEnd = layout.getLineEnd(lineNumber);
			int lineStart = layout.getLineStart(lineNumber);
			int end = getEnd(text, lineEnd, index);
			// 空白出粘贴
			if (lineStart == index) {
				if (lineStart != -1 && end != -1) {
					styleRanges.add(new StyleRange(lineStart, end, styleValue, isCheck, 0));
				}
			} else {
				// 文本中间粘贴
				int start = getStart(text, lineEnd);
				if (start == -1) {
					start = lineStart;
				}
				Logger.d("applyPasteToRichText",  "---" + start + "----" + text.charAt(start));
				// 判断自定义标签是不是在行首
				if (start != -1 && end != -1) {
					styleRanges.add(new StyleRange(start, end, styleValue, isCheck, 0));
				}
			}
		}
	}

	private int getStart(String text, int fromIndex) {
		int firstEnterIndex = text.indexOf("\n");
		if (firstEnterIndex > fromIndex) {
			return 0;
		} else {
			// 从起始位置往前查找第一个换行符
			for (int i = fromIndex - 1; i >= 1; i--) {
				if (text.charAt(i) == '\n') {
					return i + 1; // 返回换行符的位置
				}
			}
			return -1; // 如果未找到换行符，返回 -1

		}
	}

	private int getEnd(String text, int fromIndex, int tagIndex) {
		int index = text.indexOf("\n", tagIndex + 1);
		// 没有换行符，结束位置为文本长度
		if (index == -1) {
			index = text.length() - 1;
		}
		return index;
	}

	private boolean containListNumber(String text) {
		boolean isContain = false;
		try {
			if (text.contains(".")) {
				int index = text.indexOf(".");
				// 前一位不是数字，表示不是有序列表
				Integer.parseInt(text.substring(index - 1, index));
				isContain = true;
			}
		} catch (Exception e) {
			Logger.e("containListNumber", "error: " + e);
		}
		return isContain;
	}

	private int findCustomNumberTag(String text, List < StyleRange> styleRanges, Layout layout) {
		int index = -1;
		int number = 1;
		// 查找起始number, 复制的有序列表有可能不是从第一个位置 1. 复制的，从中间位置复制就需要找到正确的有序列表开始位置
		int startNumber = 0;
		int resultStartNumber = -1;
		if(containListNumber(text)) {
			int startIndex = 0;
			for(;;) {
				startIndex = text.indexOf(startNumber + ".", startIndex);
				if (startIndex != -1) {
					// 查找起始位置，适配多位数的情况
					for (int i = startIndex - 1; i >= 0; i--) {
						try {
							String tempNumber = text.substring(i, i + (startNumber + ".").length());
							Logger.d("findCustomNumberTag", "tempNumber=" + tempNumber);
							startNumber = Integer.parseInt(tempNumber);
						} catch (Exception e) {
							Logger.e("findCustomNumberTag", "error: " + e);
							// 不是数字，不需要继续查找
							break;
						}
					}
					resultStartNumber = startNumber;
					break;
				}
				startNumber++;
			}
		}
		Logger.d("findCustomNumberTag", "startNumber=" + startNumber);
		while((index = text.indexOf(startNumber + ".", index +1)) != -1){
			int lineNumber = layout.getLineForOffset(index);
			int lineEnd = layout.getLineEnd(lineNumber);
			int lineStart = layout.getLineStart(lineNumber);
			int end = getEnd(text, lineEnd, index);
			// 空白出粘贴
			if (lineStart == index) {
				if (lineStart != -1 && end != -1) {
					styleRanges.add(new StyleRange(lineStart, end, "number", false, number));
				}
			} else {
				// 文本中间粘贴
				int start = getStart(text, lineStart);
				// 判断自定义标签是不是在行首
				if (start != -1 && end != -1) {
					styleRanges.add(new StyleRange(start, end, "number", false, number));
				}
			}

			startNumber ++;
			number ++;
		}
		return resultStartNumber;
	}

	private int replace(Editable editable, String tag, List <StyleRange> styleRanges) {
		int replaceCount = 0;
		String text = editable.toString();
		Layout layout = this.getLayout();
		int index = -1;
		while((index = text.indexOf(tag, index +1)) != -1){
			if (isCustomStyle(index, layout)) {
				editable.delete(index, index + tag.length());
				text = editable.toString();
				replaceCount += tag.length();
				updateStyleRange(styleRanges, index, tag.length());
			}
		}
		return replaceCount;
	}

	/**
	 * 删除自定义符号后，样式起始位置需要重新调整
	 * @param styleRanges
	 * @param start
	 */
	private void updateStyleRange(List <StyleRange> styleRanges, int start, int tagLength) {
		List<StyleRange> ranges = new ArrayList<StyleRange>();
		int rangeStart = 0;
		int rangeEnd = 0;
		for (StyleRange range: styleRanges) {
			rangeStart = range.getStart();
			rangeEnd = range.getEnd();
			if (range.getStart() > start) {
				rangeStart = range.getStart() - tagLength;
			}
			if (range.getEnd() > start) {
				rangeEnd = range.getEnd() - tagLength;
			}
			ranges.add(new StyleRange(rangeStart, rangeEnd, range.getValue(), range.getChecked(), range.getNumber()));
		}
		styleRanges.clear();
		styleRanges.addAll(ranges);
	}

	private int replaceNumber(Editable editable, int startNumber, List <StyleRange> styleRanges) {
		int replaceCount = 0;
		String text = editable.toString();
		Layout layout = this.getLayout();
		int index = -1;
		int number = startNumber;
		while((index = text.indexOf(number + ".", index +1)) != -1){
			if (isCustomStyle(index, layout)) {
				editable.delete(index, index + (number + ".").length());
				replaceCount += (number + ".").length();
			}
			text = editable.toString();
			updateStyleRange(styleRanges, index, (number + ".").length());
			number ++;
		}
		return replaceCount;
	}

	private boolean isCustomStyle(int index, Layout layout) {
		int lineNumber = layout.getLineForOffset(index);
		int lineEnd = layout.getLineEnd(lineNumber);
		int lineStart = layout.getLineStart(lineNumber);
		return lineStart != -1 && lineEnd != -1;
	}

	// 可选的额外光标变动监听
	public interface OnSelectionChangedListener {
		void onSelectionChanged(int selStart, int selEnd);
	}

	private OnSelectionChangedListener selectionChangedListener;

	public void setOnSelectionChangedListener(OnSelectionChangedListener listener) {
		this.selectionChangedListener = listener;
	}


	/**
	 * 重写系统onSelectionChanged：
	 * - 会在用户点击、光标移动、选区改变时被调用
	 * - 这里负责同步所有样式按钮高亮状态，并实时同步当前行的对齐状态
	 * - 调用各样式Style的updateCheckStatus
	 * - 通过StyleStatusListener回报精确的状态（粗体、列表、对齐等）
	 */
	@Override
	public void onSelectionChanged(int selStart, int selEnd) {
		if (isDestroyed) return;
		if (ignoreSelectionChanged) return;
		super.onSelectionChanged(selStart, selEnd);

		if (selectionChangedListener != null) {
			selectionChangedListener.onSelectionChanged(selStart, selEnd);
		}

		// 修正列表行的光标位置（只在没有选区时执行）
		if (selStart == selEnd) {
			// 延迟执行光标修正，确保所有文本变化都已完成
			post(new Runnable() {
				@Override
				public void run() {
					Logger.d("AREditText", "onSelectionChanged: calling fixListLineCursorPosition, cursor at " + selStart);
					fixListLineCursorPosition();
				}
			});
		}

		if (!UISHOW_MONITORING) return;

		Editable editable = this.getEditableText();

		// --------------------------
		// 选区范围定义
		// --------------------------
		int selectionStart = Math.min(selStart, selEnd);
		int selectionEnd = Math.max(selStart, selEnd);
		boolean hasSelection = selectionStart != selectionEnd;

		// 段落类样式初始值[以行为单位]
		boolean allUpcoming = false, allListNumber = false, allListBullet = false;
		// 字符类样式初始值
		boolean allBold = false, allItalic = false, allUnderline = false, allStrike = false;

		// 字体颜色/背景/大小初始值
		Integer fontColor = null;
		Integer bgColor = null;
		Integer fontSize = null;

		if (hasSelection) {
			// ====================================================================
			// [选区模式] —— 工具栏仅在选区范围“所有内容均带有该样式”时高亮（样式交集机制）
			// ====================================================================

			// 段落类：选区每一行都要被对应段落span(如待办、有序、无序列表)完整覆盖
			allUpcoming = spansCoverSelectionByLine(editable, selectionStart, selectionEnd, UpcomingListSpan.class);
			allListNumber = spansCoverSelectionByLine(editable, selectionStart, selectionEnd, ListNumberSpan.class);
			allListBullet = spansCoverSelectionByLine(editable, selectionStart, selectionEnd, ListBulletSpan.class);

			// 字符类：选区每个字符都需要带该样式span
			allBold = isAllCharStyles(editable, selectionStart, selectionEnd, android.text.style.StyleSpan.class, android.graphics.Typeface.BOLD);
			allItalic = isAllCharStyles(editable, selectionStart, selectionEnd, android.text.style.StyleSpan.class, android.graphics.Typeface.ITALIC);
			allUnderline = isAllCharStyles(editable, selectionStart, selectionEnd, AreUnderlineSpan.class, null);
			allStrike = isAllCharStyles(editable, selectionStart, selectionEnd, StrikethroughSpan.class, null);

			// 字体属性：只有选区所有字符都相同时才返回属性，否则为null
			fontColor = getCommonForegroundColor(editable, selectionStart, selectionEnd);
			bgColor = getCommonBackgroundColor(editable, selectionStart, selectionEnd);
			fontSize = getCommonFontSize(editable, selectionStart, selectionEnd);
		} else {
			// ====================================================================
			// [光标模式，无选区] —— 判断光标所在位置是否被样式覆盖
			// ====================================================================

			// 检查当前行是否为空
			boolean isLineEmpty = isCurrentLineEmpty(selectionStart);

			if (isLineEmpty && mLastDeletedStyleState != null) {
				// 当前行为空且有保存的样式状态
				// 如果用户在当前行主动设置了样式，使用更新后的保存状态
				// 否则使用删除前保存的样式状态
				allBold = mLastDeletedStyleState.bold;
				allItalic = mLastDeletedStyleState.italic;
				allUnderline = mLastDeletedStyleState.underline;
				allStrike = mLastDeletedStyleState.strikethrough;
				fontColor = mLastDeletedStyleState.fontColor;
				bgColor = mLastDeletedStyleState.bgColor;
				fontSize = mLastDeletedStyleState.fontSize;

				// 段落类样式仍然按照原有逻辑判断
				int textLen = editable.length();
				int cursor = Math.max(0, Math.min(selectionStart, textLen - 1));
				int checkPos = cursor > 0 ? cursor - 1 : cursor;
				allUpcoming = hasParagraphSpanOnPos(editable, checkPos, UpcomingListSpan.class);
				allListNumber = hasParagraphSpanOnPos(editable, checkPos, ListNumberSpan.class);
				allListBullet = hasParagraphSpanOnPos(editable, checkPos, ListBulletSpan.class);
			} else {
				// 按照原有逻辑判断样式状态
				// 取光标左侧字符位置 (避免在段首时越界，始终合法)
				int textLen = editable.length();
				int cursor = Math.max(0, Math.min(selectionStart, textLen - 1));
				int checkPos = cursor > 0 ? cursor - 1 : cursor;

				// 段落类：只要当前位置有span覆盖即可高亮
				allUpcoming = hasParagraphSpanOnPos(editable, checkPos, UpcomingListSpan.class);
				allListNumber = hasParagraphSpanOnPos(editable, checkPos, ListNumberSpan.class);
				allListBullet = hasParagraphSpanOnPos(editable, checkPos, ListBulletSpan.class);

				// 字符类：判断该字符是否有span覆盖
				allBold = hasCharStyleOnPos(editable, checkPos, android.text.style.StyleSpan.class, android.graphics.Typeface.BOLD);
				allItalic = hasCharStyleOnPos(editable, checkPos, android.text.style.StyleSpan.class, android.graphics.Typeface.ITALIC);
				allUnderline = hasCharStyleOnPos(editable, checkPos, AreUnderlineSpan.class, null);
				allStrike = hasCharStyleOnPos(editable, checkPos, StrikethroughSpan.class, null);

				// 字体属性：取光标左侧字符的span，没找到为null
				fontColor = getColorOnPos(editable, checkPos);
				bgColor = getBgColorOnPos(editable, checkPos);
				fontSize = getFontSizeOnPos(editable, checkPos);

				// 如果当前行不为空，清除保存的样式状态
				if (!isLineEmpty) {
					mLastDeletedStyleState = null;
				}
			}
		}

		// =======================================================================
		// 通知各样式控制对象与UI刷新监听
		//（每个样式对象updateCheckStatus可用于按钮高亮, 同时回调控件监听StyleStatusListener以供工具栏同步）
		// =======================================================================
		if (sStylesList != null) {
			for (IARE_Style style : sStylesList) {
				// -------- 段落类 ---------
				if (style instanceof ARE_Upcoming) {
					style.updateCheckStatus(allUpcoming);
					if (mStyleStatusListener != null) mStyleStatusListener.onTodoToggled(allUpcoming);
				} else if (style instanceof ARE_ListNumber) {
					style.updateCheckStatus(allListNumber);
					if (mStyleStatusListener != null) mStyleStatusListener.onNumberedListToggled(allListNumber);
				} else if (style instanceof ARE_ListBullet) {
					style.updateCheckStatus(allListBullet);
					if (mStyleStatusListener != null) mStyleStatusListener.onBulletedListToggled(allListBullet);
					// -------- 字符类 ---------
				} else if (style instanceof ARE_Bold) {
					style.updateCheckStatus(allBold);
					if (mStyleStatusListener != null) mStyleStatusListener.onBoldToggled(allBold);
				} else if (style instanceof ARE_Italic) {
					style.updateCheckStatus(allItalic);
					if (mStyleStatusListener != null) mStyleStatusListener.onItalicToggled(allItalic);
				} else if (style instanceof ARE_Underline) {
					style.updateCheckStatus(allUnderline);
					if (mStyleStatusListener != null) mStyleStatusListener.onUnderlineToggled(allUnderline);
				} else if (style instanceof ARE_Strikethrough) {
					style.updateCheckStatus(allStrike);
					if (mStyleStatusListener != null) mStyleStatusListener.onStrikethroughToggled(allStrike);
					// -------- 字体颜色、背景、字号 ---------
				} else if(style instanceof ARE_FontColor){
					boolean hasColor = fontColor != null; style.updateCheckStatus(hasColor);
					if(mStyleStatusListener != null) mStyleStatusListener.onFontColorApplied(hasColor ? fontColor : Color.BLACK);
				} else if(style instanceof ARE_BackgroundColor){
					boolean hasBg = bgColor != null; style.updateCheckStatus(hasBg);
					if(mStyleStatusListener != null) mStyleStatusListener.onBackgroundColorApplied(hasBg ? bgColor : Color.TRANSPARENT);
				} else if(style instanceof ARE_FontSize){
					boolean hasSize = fontSize != null; style.updateCheckStatus(hasSize);
					if(mStyleStatusListener != null) mStyleStatusListener.onFontSizeApplied(hasSize ? fontSize : 16);
				}
			}
		}

		// =======================================================================
		// 其它段落属性判定如对齐方式，仍采用“光标所在行对齐span”取最后一个生效
		// =======================================================================
		if (mStyleStatusListener != null) {
			Layout.Alignment currentAlignment = Layout.Alignment.ALIGN_NORMAL;
			int selectionLine = Util.getCurrentCursorLine(this);
			int start = Util.getThisLineStart(this, selectionLine);
			int end = Util.getThisLineEnd(this, selectionLine);
			AlignmentSpan.Standard[] alignmentSpans = editable.getSpans(start, end, AlignmentSpan.Standard.class);
			if (alignmentSpans != null && alignmentSpans.length > 0) {
				currentAlignment = alignmentSpans[alignmentSpans.length - 1].getAlignment();
			}
			mStyleStatusListener.onAlignmentApplied(currentAlignment);
		}
		ARE_Alignment.updateAllAlignmentCheckStatus(this);
	}

	/**
	 * [选区模式] 判断选区的每一行是否都被该spanClass类型的span全部覆盖（用于段落高亮交集）
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @param spanClass
	 * @return
	 * @param <T>
	 */
	private <T> boolean spansCoverSelectionByLine(Editable editable, int selStart, int selEnd, Class<T> spanClass) {
		if (selStart >= selEnd) return false;
		int pos = selStart;
		while (pos < selEnd) {
			int lineEnd = TextUtils.indexOf(editable, '\n', pos);
			if (lineEnd == -1 || lineEnd > selEnd) lineEnd = selEnd;
			boolean found = false;
			T[] spans = editable.getSpans(pos, lineEnd, spanClass);
			for (T span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (spanStart <= pos && spanEnd >= lineEnd) {
					found = true;
					break;
				}
			}
			if (!found) return false;
			pos = lineEnd + 1;
		}
		return true;
	}

	/**
	 * [光标模式] 判断某一字符是否被目标段落span覆盖（光标所在即高亮）
	 * @param editable
	 * @param pos
	 * @param spanClass
	 * @return
	 * @param <T>
	 */
	private <T> boolean hasParagraphSpanOnPos(Editable editable, int pos, Class<T> spanClass) {
		T[] spans = editable.getSpans(pos, pos + 1, spanClass);
		for (T span : spans) {
			int spanStart = editable.getSpanStart(span);
			int spanEnd = editable.getSpanEnd(span);
			if (pos >= spanStart && pos < spanEnd) return true;
		}

		// 对于段落样式（如待办事项），还需要检查光标是否在span开始位置
		// 这种情况下光标应该被认为是在span内
		int selectionStart = getSelectionStart();
		spans = editable.getSpans(selectionStart, selectionStart, spanClass);
		for (T span : spans) {
			int spanStart = editable.getSpanStart(span);
			int spanEnd = editable.getSpanEnd(span);
			if (selectionStart == spanStart && spanStart < spanEnd) return true;
		}

		return false;
	}

	/**
	 * [选区模式] 判断选区每个字符是否都被目标字符样式span覆盖
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @param spanClass
	 * @param requireStyle
	 * @return
	 */
	private boolean isAllCharStyles(Editable editable, int selStart, int selEnd, Class<?> spanClass, Integer requireStyle) {
		if (selStart == selEnd) return false;
		for (int i = selStart; i < selEnd; i++) {
			if (!hasCharStyleOnPos(editable, i, spanClass, requireStyle)) return false;
		}
		return true;
	}

	/**
	 * [光标模式] 判断指定字符是否被字符样式span覆盖
	 * @param editable
	 * @param pos
	 * @param spanClass
	 * @param requireStyle
	 * @return
	 */
	private boolean hasCharStyleOnPos(Editable editable, int pos, Class<?> spanClass, Integer requireStyle) {
		Object[] spans = editable.getSpans(pos, pos + 1, spanClass);
		for (Object span : spans) {
			if (spanClass == android.text.style.StyleSpan.class && requireStyle != null) {
				int style = ((android.text.style.StyleSpan) span).getStyle();
				if (style == requireStyle || (requireStyle == android.graphics.Typeface.BOLD && style == android.graphics.Typeface.BOLD_ITALIC) || (requireStyle == android.graphics.Typeface.ITALIC && style == android.graphics.Typeface.BOLD_ITALIC)) {
					int spanStart = editable.getSpanStart(span);
					int spanEnd = editable.getSpanEnd(span);
					if (pos >= spanStart && pos < spanEnd)
						return true;
				}
			} else {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (pos >= spanStart && pos < spanEnd)
					return true;
			}
		}
		return false;
	}

	/**
	 * [选区模式] 获取选区交集的共同字体颜色，没有则返回null
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @return
	 */
	private Integer getCommonForegroundColor(Editable editable, int selStart, int selEnd) {
		Integer color = null;
		if (selStart == selEnd) return null;
		for (int i = selStart; i < selEnd; i++) {
			ForegroundColorSpan[] spans = editable.getSpans(i, i + 1, ForegroundColorSpan.class);
			Integer thisColor = null;
			for (ForegroundColorSpan span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (i >= spanStart && i < spanEnd) {
					thisColor = span.getForegroundColor();
					break;
				}
			}
			if (thisColor == null) return null;
			if (color == null) color = thisColor;
			else if (!color.equals(thisColor)) return null;
		}
		return color;
	}

	/**
	 * 获取选区共同背景色
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @return
	 */
	private Integer getCommonBackgroundColor(Editable editable, int selStart, int selEnd) {
		Integer color = null;
		if (selStart == selEnd) return null;
		for (int i = selStart; i < selEnd; i++) {
			BackgroundColorSpan[] spans = editable.getSpans(i, i + 1, BackgroundColorSpan.class);
			Integer thisColor = null;
			for (BackgroundColorSpan span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (i >= spanStart && i < spanEnd) {
					thisColor = span.getBackgroundColor();
					break;
				}
			}
			if (thisColor == null) return null;
			if (color == null) color = thisColor;
			else if (!color.equals(thisColor)) return null;
		}
		return color;
	}

	/**
	 * 获取选区共同字体大小
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @return
	 */
	private Integer getCommonFontSize(Editable editable, int selStart, int selEnd) {
		Integer size = null;
		if (selStart == selEnd) return null;
		for (int i = selStart; i < selEnd; i++) {
			AbsoluteSizeSpan[] spans = editable.getSpans(i, i + 1, AbsoluteSizeSpan.class);
			Integer thisSize = null;
			for (AbsoluteSizeSpan span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (i >= spanStart && i < spanEnd) {
					thisSize = span.getSize();
					break;
				}
			}
			if (thisSize == null) return null;
			if (size == null) size = thisSize;
			else if (!size.equals(thisSize)) return null;
		}
		return size;
	}

	/**
	 * [光标模式] 获取指定位置字符的前景色（字体颜色）。
	 * 用于无选区时，判断光标处的文字颜色对应工具栏高亮和样式展示。
	 * @param editable 富文本内容
	 * @param pos 待检查字符的起始位置（推荐用光标左侧字符下标）
	 * @return 若当前位置有ForegroundColorSpan，返回其颜色值；否则返回null
	 */
	private Integer getColorOnPos(Editable editable, int pos) {
		ForegroundColorSpan[] spans = editable.getSpans(pos, pos + 1, ForegroundColorSpan.class);
		for(ForegroundColorSpan span: spans){
			int start = editable.getSpanStart(span);
			int end = editable.getSpanEnd(span);
			if(pos >= start && pos < end) return span.getForegroundColor();
		}
		return null;
	}

	/**
	 * [光标模式] 获取指定位置字符的背景色。
	 * 用于无选区时，判断光标处的背景高亮显示（如背景色按钮高亮及样式恢复）。
	 * @param editable 富文本内容
	 * @param pos 待检查字符的起始位置
	 * @return 若当前位置有BackgroundColorSpan，返回颜色值；否则返回null
	 */
	private Integer getBgColorOnPos(Editable editable, int pos) {
		BackgroundColorSpan[] spans = editable.getSpans(pos, pos + 1, BackgroundColorSpan.class);
		for(BackgroundColorSpan span: spans){
			int start = editable.getSpanStart(span);
			int end = editable.getSpanEnd(span);
			if(pos >= start && pos < end) return span.getBackgroundColor();
		}
		return null;
	}

	/**
	 * [光标模式] 获取指定位置字符的字号（字体大小）。
	 * 用于无选区时，判断光标处的字体大小设置，配合字体大小工具栏按钮的高亮和数值显示用。
	 * @param editable 富文本内容
	 * @param pos 待检查字符的起始位置
	 * @return 若当前位置有AbsoluteSizeSpan，返回字号数值（单位px）；否则返回null
	 */
	private Integer getFontSizeOnPos(Editable editable, int pos) {
		AbsoluteSizeSpan[] spans = editable.getSpans(pos, pos + 1, AbsoluteSizeSpan.class);
		for(AbsoluteSizeSpan span: spans){
			int start = editable.getSpanStart(span);
			int end = editable.getSpanEnd(span);
			if(pos >= start && pos < end) return span.getSize();
		}
		return null;
	}
	// #End of method:: onSelectionChanged

	/**
	 * 触摸事件，支持判断点击代办item时的特殊处理等
	 * @param event The motion event.
	 * @return
	 */
	@SuppressLint("ClickableViewAccessibility")
	@Override
	public boolean onTouchEvent(MotionEvent event) {
		if (!hasFocus() && ARE_Upcoming.isTouchSpan(event, this)) {
//            if (mNote != null) mNote.setDirty(true);
			if (onContentChangedListener != null) {
				onContentChangedListener.onContentChanged(getText());
			}
			return true;//没有焦点的时候，点击代办，拦截掉事件
		}

		boolean result = super.onTouchEvent(event);

		if (event.getAction() == MotionEvent.ACTION_UP && mShouldFixCursor) {
			mShouldFixCursor = false;
			int selection = getSelectionEnd();
			if (selection != mSelection) {
				if (mSelection > getText().length()) {
					mSelection = getText().length();
				}
				if (mSelection != -1) {
					setSelection(mSelection);
				} else {
//				    Logger.e("mSelection is -1");
				}
			}
		}

		// 在触摸事件结束后修正列表行的光标位置
		if (event.getAction() == MotionEvent.ACTION_UP) {
			post(new Runnable() {
				@Override
				public void run() {
					fixListLineCursorPosition();
				}
			});
		}

		return result;
	}

	/**
	 * 某些场景下强制修正光标位置（比如样式切换后保证无异常）
	 * @param selection
	 */
	public void shouldFixCursor(int selection) {
		mShouldFixCursor = true;
		mSelection = selection;
	}

	/**
	 * 修正列表行中的光标位置
	 * 当光标位于零宽字符位置时，将其移动到零宽字符之后
	 * 修复了有序列表和无序列表光标位置不正确的问题
	 */
	private void fixListLineCursorPosition() {
		try {
			Editable editable = getEditableText();
			if (editable == null) return;

			int cursorPosition = getSelectionStart();
			if (cursorPosition < 0) return;

			Layout layout = getLayout();
			if (layout == null) return;

			// 处理光标在文本末尾的情况
			if (cursorPosition >= editable.length()) {
				if (editable.length() > 0) {
					cursorPosition = editable.length() - 1;
				} else {
					return;
				}
			}

			int line = layout.getLineForOffset(cursorPosition);
			int lineStart = layout.getLineStart(line);
			int lineEnd = layout.getLineEnd(line);

			// 检查该行是否有列表项span
			UpcomingListSpan[] upcomingSpans = editable.getSpans(lineStart, lineEnd, UpcomingListSpan.class);
			ListNumberSpan[] numberSpans = editable.getSpans(lineStart, lineEnd, ListNumberSpan.class);
			ListBulletSpan[] bulletSpans = editable.getSpans(lineStart, lineEnd, ListBulletSpan.class);

			boolean hasListSpan = (upcomingSpans != null && upcomingSpans.length > 0) ||
					(numberSpans != null && numberSpans.length > 0) ||
					(bulletSpans != null && bulletSpans.length > 0);

			// 分析整个文本的零宽字符分布
			StringBuilder fullText = new StringBuilder();
			for (int i = 0; i < Math.min(editable.length(), 50); i++) {
				char c = editable.charAt(i);
				if (c == Constants.ZERO_WIDTH_SPACE_INT) {
					fullText.append("[ZWS]");
				} else if (c == '\n') {
					fullText.append("\\n");
				} else {
					fullText.append(c);
				}
			}
			Logger.d("AREditText", "fixListLineCursorPosition: fullText(first 50 chars)='" + fullText.toString() + "'");
			Logger.d("AREditText", "fixListLineCursorPosition: line=" + line + ", lineStart=" + lineStart + ", lineEnd=" + lineEnd + ", hasListSpan=" + hasListSpan + ", cursor=" + cursorPosition);
			if (upcomingSpans != null && upcomingSpans.length > 0) {
				Logger.d("AREditText", "fixListLineCursorPosition: found UpcomingListSpan, count=" + upcomingSpans.length);
			}
			if (numberSpans != null && numberSpans.length > 0) {
				Logger.d("AREditText", "fixListLineCursorPosition: found ListNumberSpan, count=" + numberSpans.length);
			}
			if (bulletSpans != null && bulletSpans.length > 0) {
				Logger.d("AREditText", "fixListLineCursorPosition: found ListBulletSpan, count=" + bulletSpans.length);
			}

			if (hasListSpan) {
				// 检查该行是否只有零宽字符（空白列表行）
				boolean isEmptyListLine = true;
				StringBuilder lineContent = new StringBuilder();
				for (int i = lineStart; i < lineEnd; i++) {
					if (i < editable.length()) {
						char c = editable.charAt(i);
						lineContent.append(c == Constants.ZERO_WIDTH_SPACE_INT ? "[ZWS]" : c);
						if (c != Constants.ZERO_WIDTH_SPACE_INT && c != '\n') {
							isEmptyListLine = false;
							break;
						}
					}
				}

				Logger.d("AREditText", "fixListLineCursorPosition: lineContent='" + lineContent.toString() + "', isEmptyListLine=" + isEmptyListLine);

				// 修复逻辑：无论是空行还是有内容的行，都要检查光标是否在零宽字符位置
				boolean needsFix = false;
				int targetPosition = -1;

				if (isEmptyListLine) {
					// 对于空白列表行，将光标定位到零宽字符之后
					for (int i = lineStart; i < lineEnd && i < editable.length(); i++) {
						if (editable.charAt(i) == Constants.ZERO_WIDTH_SPACE_INT) {
							targetPosition = i + 1;
							needsFix = (getSelectionStart() != targetPosition);
							Logger.d("AREditText", "fixListLineCursorPosition: empty line, found zero-width char at " + i + ", target position=" + targetPosition);
							break;
						}
					}
				} else {
					// 对于有内容的列表行，检查光标是否在零宽字符位置
					if (cursorPosition < editable.length() &&
							editable.charAt(cursorPosition) == Constants.ZERO_WIDTH_SPACE_INT) {
						targetPosition = cursorPosition + 1;
						needsFix = true;
						Logger.d("AREditText", "fixListLineCursorPosition: non-empty line, cursor on zero-width char, target position=" + targetPosition);
					}
					// 额外检查：如果光标在行首且行首有零宽字符，也需要修正
					else if (cursorPosition == lineStart && lineStart < editable.length() &&
							editable.charAt(lineStart) == Constants.ZERO_WIDTH_SPACE_INT) {
						targetPosition = lineStart + 1;
						needsFix = true;
						Logger.d("AREditText", "fixListLineCursorPosition: cursor at line start with zero-width char, target position=" + targetPosition);
					}
				}

				// 执行光标修正
				if (needsFix && targetPosition > 0 && targetPosition <= editable.length()) {
					setSelection(targetPosition);
					Logger.d("AREditText", "fixListLineCursorPosition: cursor moved from " + cursorPosition + " to " + targetPosition);
				} else {
					Logger.d("AREditText", "fixListLineCursorPosition: no fix needed, needsFix=" + needsFix + ", targetPosition=" + targetPosition);
				}
			}
		} catch (Exception e) {
			Logger.e("AREditText", "Error fixing list line cursor position: " + e.getMessage());
		}
	}

	/**
	 * 覆写剪贴板菜单：拦截粘贴为html富文本时的特定场景，强制文本模式
	 * @param id
	 * @return
	 */
	@Override
	public boolean onTextContextMenuItem(int id) {
		if (id == android.R.id.copy) {
			if (mOnCopyListener != null) {
				int start = getSelectionStart();
				int end = min(getSelectionEnd(), getText().length());
				CharSequence text = Objects.requireNonNull(getText()).subSequence(start, end);
				mOnCopyListener.onCopy(start, end, text);
			}
			return true;
		}
		// 1. 优先检查最大长度超限禁止粘贴（防抖：InputFilter仅对逐字符生效，粘贴需要全量判断）
		if (id == android.R.id.paste || id == android.R.id.pasteAsPlainText) {
			ClipboardManager clipboard = (ClipboardManager) getContext().getSystemService(Context.CLIPBOARD_SERVICE);
			ClipData clip = clipboard != null ? clipboard.getPrimaryClip() : null;
			String pasteStr = "";
			if (clip != null && clip.getItemCount() > 0) {
				CharSequence cs = clip.getItemAt(0).coerceToText(getContext());
				if (cs != null) pasteStr = cs.toString();
			}
			int curLen = getText() != null ? getText().length() : 0;
			int selStart = getSelectionStart();
			int selEnd = getSelectionEnd();
			int selLen = Math.abs(selEnd - selStart);
			int wouldLength = curLen - selLen + pasteStr.length();
			if (wouldLength > maxContentLength) {
				// 粘贴后将超限，禁止粘贴
				if (onLimitListener != null) onLimitListener.onPasteLimit();
				// 截断不让粘贴
				return true;
			}
		}

		// 2. 拦截HTML富文本粘贴，强制以纯文本方式粘贴
		ClipboardManager clipboard = (ClipboardManager) getContext().getSystemService(Context.CLIPBOARD_SERVICE);
		ClipData clip = clipboard != null ? clipboard.getPrimaryClip() : null;
		Logger.d("AREditText","onTextContextMenuItem id=" + id + " --- " + (clip == null ? null : clip.getDescription().getMimeType(0)) );
		//not parse html when paste
		if (clip != null
				&& clip.getDescription() != null
				&& ClipDescription.MIMETYPE_TEXT_HTML.equals(clip.getDescription().getMimeType(0))
				&& id == android.R.id.paste) {
			// 强制转为纯文本粘贴，不走富文本，否则可能解析引擎或样式重复
			id = android.R.id.pasteAsPlainText;
		}
		// 3. 正常走系统粘贴逻辑
		return super.onTextContextMenuItem(id);
	}


	@Override
	protected void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
		setTextColor(ContextCompat.getColor(getContext(), R.color.text_title));
		updateDoneTodoColorByTheme();
	}

	/**
	 * 刷新待办事项颜色(用于处理暗黑模式切换时已完成状态的待办事项颜色不会动态切换的问题)
	 */
	private void updateDoneTodoColorByTheme() {
		Editable editable = getEditableText();
		if (editable == null) return;

		// 获取主题下最新颜色
		int doneColor = ContextCompat.getColor(getContext(), com.tcl.ai.note.base.R.color.rt_todo_block_done_color);

		UpcomingListSpan[] upcomingSpans = editable.getSpans(0, editable.length(), UpcomingListSpan.class);
		for (UpcomingListSpan span : upcomingSpans) {
			int spanStart = editable.getSpanStart(span);
			int spanEnd = editable.getSpanEnd(span);

			if (span.isChecked()) {
				// 移除原有的 ForegroundColorSpan
				ForegroundColorSpan[] colorSpans = editable.getSpans(spanStart, spanEnd, ForegroundColorSpan.class);
				for (ForegroundColorSpan colorSpan : colorSpans) {
					editable.removeSpan(colorSpan);
				}
				// 重新添加新的，颜色为最新 int
				ForegroundColorSpan newColorSpan = new ForegroundColorSpan(doneColor);
				editable.setSpan(newColorSpan, spanStart, spanEnd, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
			}
		}
	}

	// 监控开关方法
	public boolean isSTORAGE_MONITORING(){ return STORAGE_MONITORING; }
	public void startStorageMonitor() { STORAGE_MONITORING = true; }
	public void stopStorageMonitor() { STORAGE_MONITORING = false; }

	public boolean isAPPLY_MONITORING() { return APPLY_MONITORING; }
	public void startApplyMonitor() { APPLY_MONITORING = true; }
	public void stopApplyMonitor() { APPLY_MONITORING = false; }

	public boolean isUISHOW_MONITORING() { return UISHOW_MONITORING; }
	public void startUiShowMonitor() { UISHOW_MONITORING = true; }
	public void stopUiSHowMonitor() { UISHOW_MONITORING = false; }

	public void startAllMonitor() {
		startStorageMonitor();
		startApplyMonitor();
		startUiShowMonitor();
	}
	public void stopAllMonitor() {
		stopStorageMonitor();
		stopApplyMonitor();
		stopUiSHowMonitor();
	}

	/**
	 * 设置/恢复行距
	 * @param value
	 */
	public void setLineSpaceExtra(float value) {
		//setLineSpacing(value,1.0f);
	}
	public void restoreDefaultLineSpace() {
		//setLineSpacing(mLineSpace,1.0f);
	}


	/**
	 * 在当前光标处插入文本，支持选中区覆盖
	 * @param text
	 */
	public void insertTextAtCursor(CharSequence text) {
		if (text == null || text.length() == 0) return;
		Editable editable = getEditableText();
		int start = getSelectionStart();
		int end = getSelectionEnd();
		if (start < 0 || end < 0) {
			editable.insert(editable.length(), text);
			setSelection(getText().length());
		} else {
			int min = Math.min(start, end);
			int max = Math.max(start, end);
			editable.replace(min, max, text);
			setSelection(min + text.length()); // 光标移到插入后
		}
	}


	/* ----------------------
	 * Customization part
	 * ---------------------- */

	/**
	 * 手动更新段落格式的按钮状态互斥(如只允许一种列表高亮)
	 * @param style
	 * @param status
	 */
	public void updateParagraphCheckStatus(IARE_Style style, boolean status) {
		if (status) {
			boolean upComingChecked = false;
			boolean listNumberChecked = false;
			boolean listBulletChecked = false;
			if (style instanceof ARE_Upcoming) {
				upComingChecked = true;
			} else if (style instanceof ARE_ListBullet) {
				listBulletChecked = true;
			} else if (style instanceof  ARE_ListNumber) {
				listNumberChecked = true;
			}
			for (IARE_Style item : sStylesList) {
				if (item instanceof ARE_Upcoming) {
					item.updateCheckStatus(upComingChecked);
				} else if (item instanceof ARE_ListBullet) {
					item.updateCheckStatus(listBulletChecked);
				} else if (item instanceof  ARE_ListNumber) {
					item.updateCheckStatus(listNumberChecked);
				}
			}
		} else {
			style.updateCheckStatus(false);
		}
	}

	/**
	 * 绑定Note
	 * @param note
	 */
	public void setNote(Note note) {
		mNote = note;
	}

	/**
	 * 兼容某些输入法/软键盘的定制InputConnection包装
	 * @param outAttrs Fill in with attribute information about the connection.
	 * @return
	 */
	@Override
	public InputConnection onCreateInputConnection(EditorInfo outAttrs) {
		return new InputConnectionCEWrapper(super.onCreateInputConnection(outAttrs));
	}

	public Runnable getSaveContentToMemoryTask() {
		return mSaveContentToMemoryTask;
	}

	public void setSaveContentToMemoryTask(Runnable saveContentToMemoryTask) {
		this.mSaveContentToMemoryTask = saveContentToMemoryTask;
	}

	/**
	 * 设置撤销/重做状态监听器
	 */
	public void setUndoRedoStateListener(UndoRedoStateListener listener) {
		this.mUndoRedoStateListener = listener;
	}

	/**
	 * 更新撤销/重做按钮状态
	 */
	public void updateUndoRedoState(boolean canUndo, boolean canRedo) {
		if (mUndoRedoStateListener != null) {
			mUndoRedoStateListener.onUndoRedoStateChanged(canUndo, canRedo);
		}
	}

	/**
	 * undo栈被添加元素，和实际undo，redo操作无关
	 */
	public void onUndoStackAdded(@NonNull RichTextEditOperation operation) {
		if (mUndoRedoStateListener != null) {
			mUndoRedoStateListener.onUndoStackAdded(operation);
		}
	}

	/**
	 * undo栈被移除元素，和实际undo，redo操作无关
	 */
	public void onUndoStackRemoved(@NonNull RichTextEditOperation operation) {
		if (mUndoRedoStateListener != null) {
			mUndoRedoStateListener.onUndoStackRemoved(operation);
		}
	}

	/**
	 * 执行撤销操作
	 * @return 是否成功撤销
	 */
	public boolean undo() {
		setMonitoring(false);
		boolean result = mRichTextUndoRedoManager.undo();
		setMonitoring(true);
		return result;
	}

	/**
	 * 执行重做操作
	 * @return 是否成功重做
	 */
	public boolean redo() {
		setMonitoring(false);
		boolean result = mRichTextUndoRedoManager.redo();
		setMonitoring(true);
		return result;
	}

	/**
	 * 清空历史记录
	 */
	public void clearHistory() {
		mRichTextUndoRedoManager.clearHistory();
	}

	/**
	 * 判断是否可以撤销
	 */
	public boolean canUndo() {
		return mRichTextUndoRedoManager.canUndo();
	}

	/**
	 * 判断是否可以重做
	 */
	public boolean canRedo() {
		return mRichTextUndoRedoManager.canRedo();
	}
	public RichTextUndoRedoManager getRichTextUndoRedoManager() {
		return mRichTextUndoRedoManager;
	}

	/**
	 * 撤销/重做状态监听器接口
	 */
	public interface UndoRedoStateListener {
		void onUndoRedoStateChanged(boolean canUndo, boolean canRedo);
		void onUndoStackAdded(@NonNull RichTextEditOperation newOperation);
		void onUndoStackRemoved(@NonNull RichTextEditOperation newOperation);
	}

	public interface OnCopyListener {
		void onCopy(int start, int end, CharSequence text);
		void startPaste(Editable editable, int start, int end, int before, int count, boolean isDelete);
	}

	/**
	 * 请求焦点并显示光标，支持指定光标位置
	 * 不强制弹起键盘
	 */
	public void requestFocusAndShowCursor() {
		requestFocusAndShowCursor(0, false); // 默认设置到开始位置
	}

	/**
	 * 请求焦点并显示光标
	 */
	public void requestFocusAndShowCursor(int cursorPosition, boolean showKeyboard) {
		if (isDestroyed) return;

		post(() -> {
			try {
				if (!hasFocus()) {
					setFocusable(true);
					setFocusableInTouchMode(true);
					requestFocus();

					// 设置光标到开始位置
					setSelection(cursorPosition);

					// 显示键盘
					if (showKeyboard) {
						InputMethodManager imm = (InputMethodManager) getContext()
								.getSystemService(Context.INPUT_METHOD_SERVICE);
						if (imm != null) {
							imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT);
						}
					}
				}else{
					// 已有焦点，直接设置光标位置
					int textLength = Objects.requireNonNull(getText()).length();
					int targetPosition = Math.min(Math.max(0, cursorPosition), textLength);
					setSelection(targetPosition);
				}
			} catch (Exception e) {
				Logger.e("AREditText", "requestFocusAndShowCursor error: " + e.getMessage());
			}
		});
	}

	public void requestFocusAndShowKeyboard() {
		if (isDestroyed) return;

		post(() -> {
			try {
				if (!hasFocus()) {
					setFocusable(true);
					setFocusableInTouchMode(true);
					requestFocus();
				}
				// 显示键盘
				InputMethodManager imm = (InputMethodManager) getContext()
						.getSystemService(Context.INPUT_METHOD_SERVICE);
				if (imm != null) {
					imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT);
				}
			} catch (Exception e) {
				Logger.e("AREditText", "requestFocusAndShowCursor error: " + e.getMessage());
			}
		});
	}

	/**
	 * 隐藏键盘并清除焦点
	 */
	public void hideKeyboardAndClearFocus() {
		if (isDestroyed) return;

		try {
			// 隐藏键盘
			InputMethodManager imm = (InputMethodManager) getContext()
					.getSystemService(Context.INPUT_METHOD_SERVICE);
			if (imm != null) {
				imm.hideSoftInputFromWindow(getWindowToken(), 0);
			}

			setFocusable(false);
			setFocusableInTouchMode(false);
			// 清除焦点
			clearFocus();
		} catch (Exception e) {
			Logger.e("AREditText", "hideKeyboardAndClearFocus error: " + e.getMessage());
		}
	}

	public void clearEditTextFocus() {
		clearFocus();
		setFocusable(false);
		setFocusableInTouchMode(false);
	}

	public void initUndoState(Spannable text) {
		mRichTextUndoRedoManager.initState(text);
	}

	/**
	 * 完整清理资源
	 */
	public void destroy() {
		if (isDestroyed) return;
		isDestroyed = true;

		Logger.d("AREditText", "Destroying AREditText: " + this.hashCode());

		// 停止所有监控
		stopAllMonitor();

		// 清理样式列表
		clearStylesList();

		// 清理历史记录
		clearHistory();

		// 清理监听器
		setOnContentChangedListener(null);
		setOnSelectionChangedListener(null);
		setStyleStatusListener(null);
		setOnClickListener(null);
		setOnMeasureChanged(null);
		setUndoRedoStateListener(null);
		setonSelectionChanged(null);
		setOnCursorToScreenListener(null);

		// 清理TextWatcher
		if (mTextWatcher != null) {
			removeTextChangedListener(mTextWatcher);
			mTextWatcher = null;
		}

		// 清理其他引用
		mNote = null;
		mSaveContentToMemoryTask = null;
	}


	/**
	 * 删除操作分析：
	 * 1. 如果删除范围完全在同一个列表项内部：
	 *      - 若删除范围覆盖了整个列表项（包括零宽字符），则记录需要删除 span（列表前缀）
	 *      - 否则只是普通删除，不影响前缀
	 * 2. 如果删除范围跨越了多个不同列表项：
	 *      - 退化回按“行”来逐行检查被删除的内容
	 *
	 * @param s 删除前的 CharSequence
	 * @param deleteStart 删除起始位置
	 * @param deleteCount 删除长度
	 */
	private void analyzeDeleteRangeAndRecordSpans(CharSequence s, int deleteStart, int deleteCount) {
		Editable editable = (Editable) s;
		int deleteEnd = deleteStart + deleteCount;

		Layout layout = getLayout();
		if (layout == null) return;

		// 获取删除起点/终点所在的列表 span
		Object[] startListSpans = getListSpansInRange(editable, deleteStart, deleteStart);
		Object[] endListSpans   = getListSpansInRange(editable,
				Math.max(deleteEnd - 1, deleteStart),
				Math.max(deleteEnd - 1, deleteStart));

		// ------------------------
		// 情况 1：起始点和终点在同一个列表项 span 内
		// ------------------------
		if (startListSpans.length > 0 && endListSpans.length > 0 && startListSpans[0] == endListSpans[0]) {
			Object span = startListSpans[0];
			int spanStart = editable.getSpanStart(span);
			int spanEnd   = editable.getSpanEnd(span);

			// 判断是否整块内容都被删除
			if (isSelectionCoveringEntireListContent(editable, deleteStart, deleteEnd, spanStart, spanEnd)) {
				spansToRemoveAfterDelete.add(span);
			}
			return; // 不跨 item，直接结束
		}

		// ------------------------
		// 情况 2：跨越了多个不同的列表项
		// 按行遍历删除的范围进行判断
		// ------------------------
		int startLine = layout.getLineForOffset(deleteStart);
		int endLine = layout.getLineForOffset(Math.min(deleteEnd - 1, s.length() - 1));

		// 单行删除，使用单行逻辑
		if (startLine == endLine) {
			handleSingleLineListDeletion(editable, layout, deleteStart, deleteEnd, startLine, deleteCount);
			return;
		}

		// 多行删除，逐行判断
		for (int line = startLine; line <= endLine; line++) {
			int lineStart = layout.getLineStart(line);
			int lineEnd = layout.getLineEnd(line);

			// 计算本行与删除范围的交集
			int intersectionStart = Math.max(deleteStart, lineStart);
			int intersectionEnd   = Math.min(deleteEnd,   lineEnd);

			if (intersectionStart >= intersectionEnd) continue;

			// 获取该行的列表 span
			Object[] listSpans = getListSpansInRange(editable, lineStart, lineEnd);
			if (listSpans.length == 0) continue;

			// 遍历本行所有的列表 span
			for (Object span : listSpans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd   = editable.getSpanEnd(span);
				if (isSelectionCoveringEntireListContent(editable, intersectionStart, intersectionEnd, spanStart, spanEnd)) {
					spansToRemoveAfterDelete.add(span);
				}
			}
		}
	}

	/**
	 * 单行删除逻辑：
	 * - 如果该行所属的 span 的内容全部被删除（包括零宽字符），则删除前缀。
	 * - 如果仅删除了部分内容：
	 *      a. 当删除的是首字符（或行首仅有零宽字符时删除零宽字符）
	 *      b. 且当前行是 span 的首行
	 *      → 删除列表前缀
	 *
	 * @param editable Editable 文本
	 * @param layout Layout
	 * @param deleteStart 删除起始位置
	 * @param deleteEnd 删除结束位置
	 * @param line 当前删除所在行号
	 * @param deleteCount 删除长度
	 */
	private void handleSingleLineListDeletion(Editable editable, Layout layout,
											  int deleteStart, int deleteEnd,
											  int line, int deleteCount) {
		int lineStart = layout.getLineStart(line);
		int lineEnd   = layout.getLineEnd(line);

		// 获取当前行的列表 span（通常是一个）
		Object[] listSpans = getListSpansInRange(editable, lineStart, lineEnd);
		if (listSpans.length == 0) return;

		Object span = listSpans[0];
		int spanStart = editable.getSpanStart(span);
		int spanEnd   = editable.getSpanEnd(span);

		boolean shouldDeletePrefix = false;

		// 情况 1：删除范围覆盖了整个 span（包括零宽字符）
		if (isSelectionCoveringEntireListContent(editable, deleteStart, deleteEnd, spanStart, spanEnd)) {
			shouldDeletePrefix = true;
		}

		// 情况 2：部分删除，但满足首字符删除规则
		if (!shouldDeletePrefix && deleteCount == 1) {
			boolean isDeletingFirstVisibleChar =
					(deleteStart == lineStart || isOnlyZeroWidthCharsBefore(deleteStart, lineStart));

			if (isDeletingFirstVisibleChar) {
				// 还需要确保当前行是这个 span 的第一行
				int firstLineOfSpan = layout.getLineForOffset(spanStart);
				int currentLine     = layout.getLineForOffset(deleteStart);
				if (currentLine == firstLineOfSpan) {
					shouldDeletePrefix = true;
				}
			}
		}

		// 最终决定
		if (shouldDeletePrefix) {
			spansToRemoveAfterDelete.add(span);
		}
	}

	/**
	 * 判断当前删除范围是否已经覆盖了整个列表项（span）的内容。
	 * 规则：
	 * 1. 删除范围包含 spanStart ~ spanEnd 的所有字符（包括零宽字符）时，返回 true。
	 * 2. 如果 span 中只剩一个零宽字符，且删除范围覆盖它，也返回 true。
	 *
	 * @param editable Editable 文本
	 * @param deleteStart 删除的起始位置
	 * @param deleteEnd   删除的结束位置
	 * @param spanStart   当前 span 的起始位置
	 * @param spanEnd     当前 span 的结束位置
	 * @return true 表示整个列表项内容都被删除
	 */
	private boolean isSelectionCoveringEntireListContent(Editable editable,
														 int deleteStart,
														 int deleteEnd,
														 int spanStart,
														 int spanEnd) {
		// 修正删除范围到 span 范围内
		int coverStart = Math.max(deleteStart, spanStart);
		int coverEnd   = Math.min(deleteEnd,   spanEnd);

		// 删除范围完全覆盖整个 span
		if (coverStart <= spanStart && coverEnd >= spanEnd) {
			return true;
		}

		// 特殊情况：span 内容只有一个零宽字符，也算是全删
		if (spanEnd - spanStart == 1 &&
				editable.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT &&
				coverStart <= spanStart && coverEnd >= spanEnd) {
			return true;
		}
		return false;
	}


	/**
	 * 检查有序列表是否需要重新编号
	 * 如果编号不连续（如1,3,4而不是1,2,3），则需要重新编号
	 */
	private boolean checkIfRenumberingNeeded(Editable editable, ListNumberSpan[] spans) {
		if (spans == null || spans.length <= 1) {
			return false;
		}

		// 按照span在文档中的位置排序
		java.util.Arrays.sort(spans, (span1, span2) -> {
			int start1 = editable.getSpanStart(span1);
			int start2 = editable.getSpanStart(span2);
			return Integer.compare(start1, start2);
		});

		// 检查编号是否连续
		for (int i = 0; i < spans.length; i++) {
			int expectedNumber = i + 1;
			int actualNumber = spans[i].getNumber();
			if (actualNumber != expectedNumber) {
				Logger.d("AREditText", "Found non-consecutive numbering: expected " + expectedNumber + ", got " + actualNumber);
				return true;
			}
		}

		return false;
	}

	/**
	 * 获取指定范围内的所有列表span
	 */
	private Object[] getListSpansInRange(Editable editable, int start, int end) {
		java.util.List<Object> allSpans = new java.util.ArrayList<>();

		UpcomingListSpan[] upcomingSpans = editable.getSpans(start, end, UpcomingListSpan.class);
		if (upcomingSpans != null) {
			for (UpcomingListSpan span : upcomingSpans) {
				allSpans.add(span);
			}
		}

		ListBulletSpan[] bulletSpans = editable.getSpans(start, end, ListBulletSpan.class);
		if (bulletSpans != null) {
			for (ListBulletSpan span : bulletSpans) {
				allSpans.add(span);
			}
		}

		ListNumberSpan[] numberSpans = editable.getSpans(start, end, ListNumberSpan.class);
		if (numberSpans != null) {
			for (ListNumberSpan span : numberSpans) {
				allSpans.add(span);
			}
		}

		return allSpans.toArray();
	}

	/**
	 * 设置列表span的范围
	 */
	private void setListSpan(Editable editable, Object listSpan, int start, int end) {
		if (listSpan instanceof UpcomingListSpan) {
			editable.setSpan(listSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		} else if (listSpan instanceof ListBulletSpan) {
			editable.setSpan(listSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		} else if (listSpan instanceof ListNumberSpan) {
			editable.setSpan(listSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		}
	}

	/**
	 * 检查删除操作是否会导致内容合并到前一行
	 */
	private boolean checkIfContentWillMerge(int deletePosition) {
		try {
			Editable editable = getEditableText();
			if (editable == null || deletePosition <= 0) {
				return false;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return false;
			}

			// 先判断当前删除的位置和前一行是否在同一个列表 span 中
			int currentLine = layout.getLineForOffset(deletePosition);
			if (currentLine > 0) {
				int prevLine = currentLine - 1;
				int prevLineStart = layout.getLineStart(prevLine);
				int prevLineEnd = layout.getLineEnd(prevLine);
				int currLineStart = layout.getLineStart(currentLine);
				int currLineEnd = layout.getLineEnd(currentLine);

				// 获取上一行 & 当前行的 列表/待办 span
				Object[] prevSpans = getListSpansInRange(editable, prevLineStart, prevLineEnd);
				Object[] currSpans = getListSpansInRange(editable, currLineStart, currLineEnd);

				if (prevSpans.length > 0 && currSpans.length > 0) {
					for (Object ps : prevSpans) {
						for (Object cs : currSpans) {
							if (ps == cs) {
								// 同一个 span（多行列表项），不算合并
								return false;
							}
						}
					}
				}
			}

			// 检查当前行是否有内容（除了列表前缀）
			int lineStart = layout.getLineStart(currentLine);
			int lineEnd = layout.getLineEnd(currentLine);

			boolean hasRealContent = false;
			for (int i = lineStart; i < lineEnd; i++) {
				char c = editable.charAt(i);
				if (c != Constants.ZERO_WIDTH_SPACE_INT) {
					hasRealContent = true;
					break;
				}
			}

			// 特殊情况：空行但下一行有内容时算合并
			if (!hasRealContent && lineEnd < editable.length() && editable.charAt(lineEnd) == '\n') {
				if (currentLine + 1 < layout.getLineCount()) {
					int nextLineStart = layout.getLineStart(currentLine + 1);
					int nextLineEnd = layout.getLineEnd(currentLine + 1);
					if (nextLineEnd > nextLineStart && nextLineEnd <= editable.length() &&
							editable.charAt(nextLineEnd - 1) == '\n') {
						nextLineEnd--;
					}
					for (int i = nextLineStart; i < nextLineEnd; i++) {
						char c = editable.charAt(i);
						if (c != Constants.ZERO_WIDTH_SPACE_INT) {
							hasRealContent = true;
							break;
						}
					}
				}
			}

			return hasRealContent;

		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 查找内容合并的目标列表span（前一行的列表span）
	 */
	private Object findTargetListSpanForMerge(int deletePosition) {
		try {
			Editable editable = getEditableText();
			if (editable == null || deletePosition <= 0) {
				return null;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return null;
			}

			// 找到前一行
			int currentLine = layout.getLineForOffset(deletePosition);
			if (currentLine <= 0) {
				return null; // 已经是第一行
			}

			int prevLine = currentLine - 1;
			int prevLineStart = layout.getLineStart(prevLine);
			int prevLineEnd = layout.getLineEnd(prevLine);

			// 查找前一行的列表span
			UpcomingListSpan[] upcomingSpans = editable.getSpans(prevLineStart, prevLineEnd, UpcomingListSpan.class);
			if (upcomingSpans != null && upcomingSpans.length > 0) {
				return upcomingSpans[0];
			}

			ListBulletSpan[] bulletSpans = editable.getSpans(prevLineStart, prevLineEnd, ListBulletSpan.class);
			if (bulletSpans != null && bulletSpans.length > 0) {
				return bulletSpans[0];
			}

			ListNumberSpan[] numberSpans = editable.getSpans(prevLineStart, prevLineEnd, ListNumberSpan.class);
			if (numberSpans != null && numberSpans.length > 0) {
				return numberSpans[0];
			}

			return null;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 在内容合并后扩展列表span范围
	 */
	private void expandListSpanAfterMerge(Editable editable, Object listSpan) {
		if (listSpan == null) {
			return;
		}

		// 获取当前span的范围
		int spanStart = editable.getSpanStart(listSpan);
		int spanEnd = editable.getSpanEnd(listSpan);

		if (spanStart < 0 || spanEnd < 0) {
			return;
		}

		// 使用Layout来准确计算span应该覆盖的完整范围
		Layout layout = getLayout();
		if (layout != null) {
			try {
				// 找到span开始位置所在的行
				int startLine = layout.getLineForOffset(spanStart);
				int startLineStart = layout.getLineStart(startLine);

				// 计算新的span结束位置：从span开始位置找到下一个换行符或文本结束
				int textLength = editable.length();
				int newSpanEnd = spanStart;

				while (newSpanEnd < textLength) {
					char c = editable.charAt(newSpanEnd);
					if (c == '\n') {
						break; // 找到换行符，不包含换行符本身
					}
					newSpanEnd++;
				}

				// 确保新的结束位置不小于原来的结束位置（防止span范围缩小）
				if (newSpanEnd < spanEnd) {
					newSpanEnd = spanEnd;
				}

				// 检查是否需要扩展span范围
				boolean needsExpansion = (spanStart > startLineStart) || (spanEnd < newSpanEnd);

				if (needsExpansion) {
					// 移除原span
					editable.removeSpan(listSpan);

					// 重新设置span范围：从行开始到合并后的内容结束
					setListSpan(editable, listSpan, startLineStart, newSpanEnd);

					// 如果是已完成的待办事项，需要为新范围加删除线
					if (listSpan instanceof UpcomingListSpan) {
						UpcomingListSpan upcoming = (UpcomingListSpan) listSpan;
						if (upcoming.isChecked()) {
							ARE_Upcoming helper = new ARE_Upcoming(getContext());
							helper.setEditText(this);
							helper.setStrikeboundSpan(startLineStart, newSpanEnd);
						}
					}


					// 强制刷新工具栏状态
					post(new Runnable() {
						@Override
						public void run() {
							int currentSelection = getSelectionStart();
							onSelectionChanged(currentSelection, currentSelection);
						}
					});
				}
			} catch (Exception e) {
				// 忽略异常
			}
		} else {
			// 如果Layout为null，使用原来的逻辑作为备用
			// 计算新的span结束位置（到下一个换行符或文本结束）
			int textLength = editable.length();
			int newSpanEnd = spanStart;

			while (newSpanEnd < textLength) {
				char c = editable.charAt(newSpanEnd);
				if (c == '\n') {
					break; // 找到换行符，不包含换行符本身
				}
				newSpanEnd++;
			}

			// 强制扩展span范围，即使newSpanEnd不大于spanEnd
			if (newSpanEnd != spanEnd) {

				// 移除原span
				editable.removeSpan(listSpan);

				// 重新设置span范围
				setListSpan(editable, listSpan, spanStart, newSpanEnd);

				// 待办完成状态下补充删除线
				if (listSpan instanceof UpcomingListSpan) {
					UpcomingListSpan upcoming = (UpcomingListSpan) listSpan;
					if (upcoming.isChecked()) {
						ARE_Upcoming helper = new ARE_Upcoming(getContext());
						helper.setEditText(this);
						helper.setStrikeboundSpan(spanStart, newSpanEnd);
					}
				}

				// 强制刷新工具栏状态
				post(new Runnable() {
					@Override
					public void run() {
						int currentSelection = getSelectionStart();
						onSelectionChanged(currentSelection, currentSelection);
					}
				});
			}
		}
	}

	/**
	 * 扩展列表span的范围以包含合并的内容
	 * 当删除操作导致内容合并到列表项时，需要扩展span范围
	 */
	private void expandListSpanToIncludeMergedContent(Editable editable, Object listSpan, int deletePosition) {
		// 获取当前span的范围
		int currentSpanStart = editable.getSpanStart(listSpan);
		int currentSpanEnd = editable.getSpanEnd(listSpan);

		if (currentSpanStart < 0 || currentSpanEnd < 0) {
			return;
		}

		// 找到span开始位置所在行的结束位置（合并后的行）
		// 不依赖Layout，直接从span开始位置查找到下一个换行符或文本结束
		int newSpanEnd = currentSpanStart;
		int textLength = editable.length();

		while (newSpanEnd < textLength) {
			char c = editable.charAt(newSpanEnd);
			if (c == '\n') {
				break; // 找到换行符，不包含换行符本身
			}
			newSpanEnd++;
		}

		// 如果需要扩展span范围
		if (newSpanEnd > currentSpanEnd) {

			// 移除原span
			editable.removeSpan(listSpan);

			// 重新设置span范围，覆盖整个合并后的行
			setListSpan(editable, listSpan, currentSpanStart, newSpanEnd);

			// 如果是已完成待办，给新范围加删除线
			if (listSpan instanceof UpcomingListSpan) {
				UpcomingListSpan upcoming = (UpcomingListSpan) listSpan;
				if (upcoming.isChecked()) {
					ARE_Upcoming helper = new ARE_Upcoming(getContext());
					helper.setEditText(this);
					helper.setStrikeboundSpan(currentSpanStart, newSpanEnd);
				}
			}
		}

		// 延迟执行强制修复，确保所有删除操作完成后再处理
		post(new Runnable() {
			@Override
			public void run() {
				forceFixListSpanRange(editable, listSpan);
			}
		});
	}

	/**
	 * 强制修复列表span范围，确保span覆盖整个合并后的行
	 * 这是一个保险措施，用于处理常规扩展可能遗漏的情况
	 */
	private void forceFixListSpanRange(Editable editable, Object listSpan) {
		// 再次检查span是否还存在
		int spanStart = editable.getSpanStart(listSpan);
		int spanEnd = editable.getSpanEnd(listSpan);

		if (spanStart < 0 || spanEnd < 0) {
			return; // span已被删除
		}

		// 直接从span开始位置计算到行尾
		int textLength = editable.length();
		int correctSpanEnd = spanStart;

		while (correctSpanEnd < textLength) {
			char c = editable.charAt(correctSpanEnd);
			if (c == '\n') {
				break; // 找到换行符，不包含换行符本身
			}
			correctSpanEnd++;
		}

		// 如果当前span范围不正确，重新设置
		if (correctSpanEnd != spanEnd) {

			// 移除原span
			editable.removeSpan(listSpan);

			// 重新设置正确的span范围
			setListSpan(editable, listSpan, spanStart, correctSpanEnd);

			// 强制刷新工具栏状态
			post(new Runnable() {
				@Override
				public void run() {
					int currentSelection = getSelectionStart();
					onSelectionChanged(currentSelection, currentSelection);
				}
			});
		}
	}

	/**
	 * 修复所有列表span的范围，确保它们正确覆盖各自的行
	 * 这是一个通用的修复方法，用于处理删除操作后可能出现的span范围问题
	 */
	private void fixAllListSpanRangesAfterDelete() {
		try {
			Editable editable = getEditableText();
			if (editable == null) {
				return;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return;
			}

			// 获取所有列表span并直接修复，而不是按行处理
			// 这样可以更好地处理跨多行的列表项
			UpcomingListSpan[] upcomingSpans = editable.getSpans(0, editable.length(), UpcomingListSpan.class);
			ListBulletSpan[] bulletSpans = editable.getSpans(0, editable.length(), ListBulletSpan.class);
			ListNumberSpan[] numberSpans = editable.getSpans(0, editable.length(), ListNumberSpan.class);

			// 修复待办事项span
			for (UpcomingListSpan span : upcomingSpans) {
				fixSpanToWholeLine(editable, span);
			}

			// 修复无序列表span
			for (ListBulletSpan span : bulletSpans) {
				fixSpanToWholeLine(editable, span);
			}

			// 修复有序列表span
			for (ListNumberSpan span : numberSpans) {
				fixSpanToWholeLine(editable, span);
			}

			// 强制刷新工具栏状态
			int currentSelection = getSelectionStart();
			onSelectionChanged(currentSelection, currentSelection);
		} catch (Exception e) {
			Logger.e("AREditText", "Error : " + e.getMessage());
		}
	}

	/**
	 * 修复单个span使其覆盖完整的列表项内容
	 * 对于跨多行的列表项，需要确保span覆盖从列表开始到内容结束的完整范围
	 */
	private void fixSpanToWholeLine(Editable editable, Object span) {
		try {
			int currentSpanStart = editable.getSpanStart(span);
			int currentSpanEnd = editable.getSpanEnd(span);

			if (currentSpanStart < 0 || currentSpanEnd < 0) {
				return; // span已被删除
			}

			// 对于列表span，需要确保它从列表项开始位置覆盖到内容的实际结束位置
			// 而不是仅仅覆盖当前行

			// 找到列表项的实际开始位置（通常是零宽字符的位置）
			int actualStart = currentSpanStart;

			// 找到列表项内容的实际结束位置（到下一个换行符或文本结束）
			int textLength = editable.length();
			int actualEnd = currentSpanStart;

			while (actualEnd < textLength) {
				char c = editable.charAt(actualEnd);
				if (c == '\n') {
					break; // 找到换行符，不包含换行符本身
				}
				actualEnd++;
			}

			// 确保新的结束位置不小于原来的结束位置
			actualEnd = Math.max(actualEnd, currentSpanEnd);

			// 检查span是否需要修复
			boolean needsFix = (currentSpanStart != actualStart) || (currentSpanEnd != actualEnd);

			if (needsFix) {
				// 移除原span
				editable.removeSpan(span);

				// 重新设置span范围覆盖完整的列表项内容
				setListSpan(editable, span, actualStart, actualEnd);
			}
		} catch (Exception e) {
			Logger.e("AREditText", "Error : " + e.getMessage());
		}
	}

	/**
	 * 绘制选中列表项的背景
	 */
	private void drawSelectedListPrefixBackgrounds(Canvas canvas) {
		Editable editable = getEditableText();
		Layout layout = getLayout();
		if (editable == null || layout == null) return;

		int selStart = Math.max(0, Math.min(getSelectionStart(), getSelectionEnd()));
		int selEnd = Math.max(0, Math.max(getSelectionStart(), getSelectionEnd()));
		if (selStart == selEnd) return;

		final int paddingLeft = getTotalPaddingLeft();
		final int paddingTop = getTotalPaddingTop();
		final int scrollY = getScrollY();
		final int dp21 = DisplayUtils.dp2px(21);

		// 收集被选区“被完全覆盖”的列表 span
		List<Object> covered = new ArrayList<>();
		UpcomingListSpan[] allUpcoming = editable.getSpans(0, editable.length(), UpcomingListSpan.class);
		ListBulletSpan[] allBullets = editable.getSpans(0, editable.length(), ListBulletSpan.class);
		ListNumberSpan[] allNumbers = editable.getSpans(0, editable.length(), ListNumberSpan.class);

		if (allUpcoming != null) {
			for (Object s : allUpcoming) {
				int a = editable.getSpanStart(s), b = editable.getSpanEnd(s);
				if (a >= 0 && b > a && isSpanFullyCoveredBySelection(editable, selStart, selEnd, a, b)) {
					covered.add(s);
				}
			}
		}
		if (allBullets != null) {
			for (Object s : allBullets) {
				int a = editable.getSpanStart(s), b = editable.getSpanEnd(s);
				if (a >= 0 && b > a && isSpanFullyCoveredBySelection(editable, selStart, selEnd, a, b)) {
					covered.add(s);
				}
			}
		}
		if (allNumbers != null) {
			for (Object s : allNumbers) {
				int a = editable.getSpanStart(s), b = editable.getSpanEnd(s);
				if (a >= 0 && b > a && isSpanFullyCoveredBySelection(editable, selStart, selEnd, a, b)) {
					covered.add(s);
				}
			}
		}
		if (covered.isEmpty()) return;

		// 按文档顺序排序
		Collections.sort(covered, (o1, o2) -> Integer.compare(editable.getSpanStart(o1), editable.getSpanStart(o2)));

		// 要绘制的项：如果 covered <= 2 则全部绘制，否则仅首尾两项
		List<Object> toDraw = new ArrayList<>();
		if (covered.size() <= 2) {
			toDraw.addAll(covered);
		} else {
			toDraw.add(covered.get(0));
			toDraw.add(covered.get(covered.size() - 1));
		}

		// 判断选区内是否包含非列表内容（用于判定“跨块”）
		boolean selectionContainsNonListContent = false;
		int i = selStart;
		while (i < selEnd) {
			Object[] spansAt = getListSpansInRange(editable, i, Math.min(i + 1, editable.length()));
			if (spansAt == null || spansAt.length == 0) {
				selectionContainsNonListContent = true;
				break;
			}
			// 若当前位置有 span，跳到该 span 末尾以加速检查
			Object first = spansAt[0];
			int spanEnd = editable.getSpanEnd(first);
			if (spanEnd <= i) {
				i++; // 防止死循环
			} else {
				i = Math.min(spanEnd, selEnd);
			}
		}

		// 记录选区起/尾处的 span（若存在）
		Object[] spansAtSelStart = getListSpansInRange(editable, selStart, Math.min(selStart + 1, editable.length()));
		Object firstSpanAtSelStart = spansAtSelStart.length > 0 ? spansAtSelStart[0] : null;
		Object[] spansAtSelEnd = getListSpansInRange(editable, Math.max(selEnd - 1, 0), Math.min(selEnd, editable.length()));
		Object lastSpanAtSelEnd = spansAtSelEnd.length > 0 ? spansAtSelEnd[0] : null;

		// 判断是否“选区严格等于单个被覆盖的列表项”（用于 -21 情形）
		boolean selectionEqualsSingleCoveredSpan = false;
		Object onlyCoveredSpan = null;
		if (covered.size() == 1) {
			Object only = covered.get(0);
			int a = editable.getSpanStart(only), b = editable.getSpanEnd(only);
			if (a >= 0 && b > a && isSpanFullyCoveredBySelection(editable, selStart, selEnd, a, b)) {
				// 再确保选区内没有其它非该 span 的可见内容
				boolean onlyThatSpan = true;
				for (int p = selStart; p < selEnd; ) {
					Object[] spansAt = getListSpansInRange(editable, p, Math.min(p + 1, editable.length()));
					if (spansAt == null || spansAt.length == 0) { onlyThatSpan = false; break; }
					boolean inSame = false;
					for (Object ss : spansAt) if (ss == only) { inSame = true; break; }
					if (!inSame) { onlyThatSpan = false; break; }
					int spanEndP = editable.getSpanEnd(spansAt[0]);
					if (spanEndP <= p) p++; else p = Math.min(spanEndP, selEnd);
				}
				if (onlyThatSpan) {
					selectionEqualsSingleCoveredSpan = true;
					onlyCoveredSpan = only;
				}
			}
		}

		// 绘制
		final int SYMBOL_MARGIN = DisplayUtils.dp2px(13);
		final int BG_EXTRA = DisplayUtils.dp2px(13);
		final int BG_WIDTH_BULLET = DisplayUtils.dp2px(20);

		for (Object obj : toDraw) {
			int spanStart = editable.getSpanStart(obj);
			int spanEnd = editable.getSpanEnd(obj);
			if (spanStart < 0 || spanEnd <= spanStart) continue;

			int line = layout.getLineForOffset(spanStart);
			int lineTop = layout.getLineTop(line) + paddingTop - scrollY;
			int rawLineBottom = layout.getLineBottom(line) + paddingTop - scrollY;

			int lineBottom;
			if (selectionEqualsSingleCoveredSpan && onlyCoveredSpan == obj) {
				// 选区严格等于单个列表项 -> 使用缩短
				lineBottom = rawLineBottom - (CommonUtilsKt.isTablet()?21:25);
			} else {
				boolean isFirstCovered = (covered.get(0) == obj);
				boolean isLastCovered = (covered.get(covered.size() - 1) == obj);

				boolean firstCondition = false;
				if (isFirstCovered && firstSpanAtSelStart != null && firstSpanAtSelStart == obj) {
					int fs = editable.getSpanStart(firstSpanAtSelStart);
					int fe = editable.getSpanEnd(firstSpanAtSelStart);
					if (selStart < fs || selEnd > fe) firstCondition = true; // 跨出首 span -> 首项不减
				}

				boolean lastCondition = false;
				if (isLastCovered) {
					// 修改：只要 covered.size() >= 2（即选区包含连续多个列表项），最后一个都应缩短 -(平板：21，手机：25)
					if (covered.size() >= 2) {
						lastCondition = true;
					} else if (selectionContainsNonListContent && lastSpanAtSelEnd != null && lastSpanAtSelEnd == obj) {
						// 或 选区跨块且末端在该列表 span -> 缩短 -(平板：21，手机：25)
						lastCondition = true;
					}
				}

				if (firstCondition) {
					lineBottom = rawLineBottom; // 首项（跨块）不减
				} else if (lastCondition) {
					lineBottom = rawLineBottom - (CommonUtilsKt.isTablet()?21:25); // 最后项按规则减 (平板：21，手机：25)
				} else {
					lineBottom = rawLineBottom;
				}
			}

			if (lineTop >= lineBottom) continue;

			// prefix x 与 drawLeadingMargin 对齐
			float baseX = paddingLeft - getScrollX();
			int dir = layout.getParagraphDirection(line);
			float prefixX = baseX + dir * SYMBOL_MARGIN;

			float left = prefixX - BG_EXTRA;
			float right = prefixX + BG_WIDTH_BULLET - 10;
			if (left > right) { float t = left; left = right; right = t; }

			left = Math.max(0f, left);
			right = Math.min((float) getWidth(), right);
			if (left < right) {
				canvas.drawRect(left, lineTop, right, lineBottom, mPrefixBgPaint);
			}
		}
	}

	/**
	 * 判断 span 是否“被选区完全覆盖（忽略换行与零宽）
	 */
	private boolean isSpanFullyCoveredBySelection(Editable editable, int selStart, int selEnd, int spanStart, int spanEnd) {
		if (spanStart < 0 || spanEnd <= spanStart) return false;
		int len = Math.min(spanEnd, editable.length());
		boolean hasVisible = false;
		for (int i = spanStart; i < len; i++) {
			char c = editable.charAt(i);
			if (c == '\n' || c == '\u200B') continue; // 忽略换行与零宽
			hasVisible = true;
			// 要求可见字符在选区内：selStart <= i < selEnd
			if (i < selStart || i >= selEnd) return false;
		}
		if (!hasVisible) {
			// 若没有可见字符（极端情况），退回到严格包含判断以避免误判空项
			return selStart <= spanStart && selEnd >= spanEnd;
		}
		return true;
	}
}
