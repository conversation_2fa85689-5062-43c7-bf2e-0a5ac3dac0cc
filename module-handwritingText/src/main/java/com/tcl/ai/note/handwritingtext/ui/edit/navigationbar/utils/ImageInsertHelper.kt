package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils

import android.content.Context
import android.graphics.RectF
import android.net.Uri
import androidx.core.net.toUri
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.repo.NoteRepository2
import com.tcl.ai.note.handwritingtext.utils.ImageUtils
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import java.io.File

object ImageInsertHelper {

    /**
     * 插入图片到画板
     */
    suspend fun insertImageToDrawBoard(
        context: Context,
        uri: Uri,
        imagePath: String,
        showRectF: RectF,
        suniaDrawViewModel: SuniaDrawViewModel,
        richTextViewModel: RichTextViewModel2,
        offset: Int = 0
    ) {
        // 插入到绘图画板
        suniaDrawViewModel.insertBitmap(imagePath, showRectF, offset)
        // 同时更新RichTextViewModel2的图片列表，确保埋点统计正确
        val currentImages = richTextViewModel.uiState.value.images.toMutableList()
        val imageCount = suniaDrawViewModel.imageCountState.value
        ImageUtils.handleImageUri(context, uri)?.let { uri ->
            Logger.d("insertImageToDrawBoard", "uri: $uri imageCount $imageCount")
            currentImages.add(EditorContent.ImageBlock(uri = uri))
            richTextViewModel.onImagesChanged(currentImages)
            // 不需要单独更新firstPicture 会一起保存
//                suniaDrawViewModel.viewModelScope.launchIO {
//                    val noteId = suniaDrawViewModel.noteId
//                    if (noteId != null) {
//                        NoteRepository2.updateFirstPicture(noteId, uri.toString())
//                    }
//                }
        }
    }
}