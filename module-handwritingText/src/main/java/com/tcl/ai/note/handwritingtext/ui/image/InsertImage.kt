package com.tcl.ai.note.handwritingtext.ui.image

import android.graphics.RectF
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.utils.isTablet

object InsertImage {

    const val MAX_IMAGE_LIMIT = 50

    val INSERT_IMAGE_OFFSET_X = if (isTablet) 48 else 24
    val WIDTH_SCALE = if (isTablet) 3 else 2
    const val INSERT_IMAGE_OFFSET_Y = 78



    fun getInsertImageRect(width: Int, height: Int, showRectF: RectF, offset: Int = 0): RectF {
        val defaultOffsetX = DisplayUtils.dp2px(INSERT_IMAGE_OFFSET_X)
        val defaultOffsetY = DisplayUtils.dp2px(INSERT_IMAGE_OFFSET_Y)
        val widthScale = 1.0f * (showRectF.width() / WIDTH_SCALE) / width
        val heightScale = 1.0f * (showRectF.height() - defaultOffsetY) / height
        val scale = minOf(widthScale, heightScale)
        if (scale >= 1.0f) {
            val offsetX = showRectF.left + (showRectF.width() - width) / 2
            val offsetY = defaultOffsetY
            return RectF(offsetX.toFloat(), offsetY.toFloat() + offset.toFloat(), offsetX.toFloat() + width.toFloat(), offsetY.toFloat() + offset.toFloat() + height.toFloat())
        } else {
            val newWidth = (width * scale).toInt()
            val newHeight = (height * scale).toInt()
            var offsetX = showRectF.left + (showRectF.width() - newWidth) / 2
            val offsetY = defaultOffsetY
            return RectF(offsetX.toFloat(), offsetY.toFloat() + offset.toFloat(), offsetX.toFloat() + newWidth.toFloat(), offsetY.toFloat() + offset.toFloat() + newHeight.toFloat())

        }
    }
}