package com.tcl.ai.note.handwritingtext.ui.richtext.base

import android.graphics.Rect
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.view.View
import com.tcl.ai.note.handwritingtext.ui.richtext.base.cursorhandle.InsertCursorHandleView
import com.tcl.ai.note.utils.Logger

class InsertHandleViewWithActionMode(val editView: BaseRichTextEditView) {
    // 正在显示的插入菜单栏
    private var curActionMode: ActionMode? = null
    // 插入水滴
    private val insertCursorHandleView = InsertCursorHandleView(editView)
    // 是否在显示菜单栏
    val isShowingActionMode get() = curActionMode != null
    // 是否在显示光标拖动柄
    val isShowingCursorHandle get() = insertCursorHandleView.isShowing

    // 自定义选区菜单
    private val insertionActionModeCallback = object : ActionMode.Callback2() {
        override fun onGetContentRect(mode: ActionMode?, view: View?, outRect: Rect?) {
            super.onGetContentRect(mode, view, outRect)
            if (view == null) return
            val cursorPointF = editView.getCursorPointF(index = editView.selectionStart, includeScale = false)
            // 插入柄的菜单栏放置的位置
            outRect?.set(
                cursorPointF.x.toInt(),
                cursorPointF.y.toInt(),
                cursorPointF.x.toInt(),
                cursorPointF.y.toInt()
            )
            Logger.i(TAG, "customInsertionActionModeCallback onGetContentRect: view: $view, outRect: $outRect")
        }

        override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            mode?.menu?.clear()
            Logger.i(TAG, "customInsertionActionModeCallback onCreateActionMode")
            val systemContext = editView.context.createPackageContext("android", 0)
            val selectAllStr = systemContext.getString(android.R.string.selectAll)
            val pasteStr = systemContext.getString(android.R.string.paste)
            val autofillStr = systemContext.getString(android.R.string.autofill)
            if (editView.canPaste()) {
                // 添加粘贴 Paste
                menu?.add(0, android.R.id.paste, 0, pasteStr)
            }
            // 添加全选 Select All
            menu?.add(0, android.R.id.selectAll, 1, selectAllStr)
            if (editView.canRequestAutofill()) {
                // 添加Auto Fill
                menu?.add(0, android.R.id.autofill, 2, autofillStr)
            }
            insertCursorHandleView.showOrUpdate()
            return true
        }

        override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            updateInsertCursorHandle()
            Logger.d(TAG, "customInsertionActionModeCallback onPrepareActionMode")
            return true
        }

        override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
            when (item?.itemId) {
                android.R.id.selectAll -> {
                    editView.onTextContextMenuItem(item.itemId)
                }

                android.R.id.paste -> {
                    editView.onTextContextMenuItem(item.itemId)
                    editView.post {
                        // 原生逻辑，粘贴后。水滴拖动柄会消失
                        hideInsertCursorHandle()
                    }
                }

                android.R.id.autofill -> {
                    editView.onTextContextMenuItem(item.itemId)
                }
            }
            return true
        }

        override fun onDestroyActionMode(mode: ActionMode?) {
            // 需要自己调用finish。不然无法隐藏
        }
    }

    init {
        editView.customInsertionActionModeCallback = insertionActionModeCallback
        insertCursorHandleView.onInsertHandleMovePosition = {
            showInsertCursorHandle()
        }
        insertCursorHandleView.onClick = {
            // 弹出菜单栏
            showInsertionActionMode()
        }
        insertCursorHandleView.onDismiss = {
            // 因为插入拖动柄是自己执行了startActionMode，所以要自己隐藏
            hideInsertionActionMode()
        }
    }

    // 显示插入光标拖动柄的菜单栏, 也就是粘贴，全选功能。
    private fun showInsertionActionMode() {
        if (curActionMode != null) {
            updateInsertionActionMode()
            return
        }
        curActionMode = editView.startActionMode(insertionActionModeCallback, ActionMode.TYPE_FLOATING)
    }

    // 更新插入光标拖动柄的菜单栏的位置
    private fun updateInsertionActionMode() {
       curActionMode?.invalidateContentRect()
    }

    // 隐藏插入光标拖动柄的菜单栏, 也就是粘贴，全选功能。
    // 因为插入拖动柄是自己执行了startActionMode，所以要自己隐藏
    private fun hideInsertionActionMode() {
        curActionMode?.finish()
        curActionMode = null
    }

    /**
     * 显示插入光标拖动柄
     *
     * 受customSelectionActionModeCallback的显示影响
     */
    private val delayDismissRunnable = Runnable {
        if (curActionMode != null) {
            return@Runnable
        }
        hideInsertCursorHandle()
    }

    fun showInsertCursorHandle() {
        hideInsertionActionMode()
        insertCursorHandleView?.showOrUpdate()
        editView.removeCallbacks(delayDismissRunnable)
        editView.postDelayed(delayDismissRunnable, 5000)
    }

    /**
     * 更新插入拖动柄的位置，同时更新菜单栏位置
     */
    fun updateInsertCursorHandle() {
        insertCursorHandleView?.updatePosition()
        updateInsertionActionMode()
    }

    /**
     * 隐藏插入光标拖动柄
     *
     * 受customSelectionActionModeCallback的显示影响
     */
    fun hideInsertCursorHandle() {
        insertCursorHandleView?.dismiss()
        hideInsertionActionMode()
    }

    companion object {
        private const val TAG = "InsertHandleViewWithActionMode"
    }
}