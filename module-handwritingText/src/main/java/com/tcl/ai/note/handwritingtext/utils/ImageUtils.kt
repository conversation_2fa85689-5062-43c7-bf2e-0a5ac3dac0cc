package com.tcl.ai.note.handwritingtext.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.RectF
import android.media.ExifInterface
import android.net.Uri
import android.provider.MediaStore
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.handwritingtext.ui.image.InsertImage
import com.tcl.ai.note.handwritingtext.utils.FileUtils.checkUriExists
import com.tcl.ai.note.handwritingtext.utils.FileUtils.copyUriToInternalStorage
import com.tcl.ai.note.utils.Logger

/**
 * 图片处理工具类
 */
object ImageUtils {
    // 创建图片存储URI
    fun createPhotoUri(context: Context): Uri? {
        val contentValues = ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, "photo_${System.currentTimeMillis()}.jpg")
            put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
        }
        return context.contentResolver.insert(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            contentValues
        )
    }

    // 处理图片URI
    fun handleImageUri(context: Context, uri: Uri): Uri? {
        return if (uri.scheme == "content") {
            // 处理系统相册URI，复制到应用私有目录
            copyUriToInternalStorage(context,uri)
            // 还可实现图片其他处理逻辑，例如压缩、存储等
        } else {
            uri
        }
    }

    // 回显时验证URI有效性
    fun loadImageUri(context: Context, uriString: String): Uri? {
        return try {
            val uri = Uri.parse(uriString)
            if (checkUriExists(context,uri)) uri else null
        } catch (e: Exception) {
            null
        }
    }

    fun getImageInfoForUri(context: Context, uri: Uri): Pair<String, Pair<Int, Int>>? {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        try {
            cursor?.let {
                cursor.moveToFirst()
                val path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA))
                var width = 0
                var height = 0

                getImageWidthAndHeight(path)?.let {
                    width = it.first
                    height = it.second
                }
                Logger.d("getImageInfoForUri", "path: $path, width: $width, height: $height")
                return Pair(path, Pair(width, height))
            }
        } catch (e: Exception) {
            Logger.e("getImageInfoForUri", "getImageInfoForUri error: ${e.message}")
        } finally {
            cursor?.close()
        }
        return null
    }



    fun getImageWidthAndHeight(path: String): Pair<Int, Int>? {
        try {
            val exif = ExifInterface(path)
            val orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL)
            var rotation = 0
            if (orientation == ExifInterface.ORIENTATION_ROTATE_90 || orientation == ExifInterface.ORIENTATION_ROTATE_270)  {
                rotation = 90
            }

            val options = BitmapFactory.Options()
            options.inJustDecodeBounds = true
            BitmapFactory.decodeFile(path, options)
            // 根据旋转角度获取正确宽高
            return if (rotation == 90) {
                Pair(options.outHeight, options.outWidth)
            } else {
                Pair(options.outWidth, options.outHeight)
            }
        } catch (e: Exception) {
            Logger.e("getImageWidthAndHeight", "getImageWidthAndHeight error: ${e.message}")
        }
        return null
    }

    /**
     * 计算图片插入的显示区域
     * @return Pair<imagePath, showRectF> 或 null
     */
    fun calculateImageInsertRect(context: Context, uri: Uri, drawRectF: RectF, offset: Int = 0): Pair<String, RectF>? {
        val imageInfo = getImageInfoForUri(context, uri) ?: return null

        val imagePath = imageInfo.first
        val width = imageInfo.second.first
        val height = imageInfo.second.second
        
        if (imagePath.isBlank() || width <= 0 || height <= 0) {
            return null
        }
        
        val showRectF = InsertImage.getInsertImageRect(width, height, drawRectF, offset)
        Logger.d("calculateImageInsertRect", "showRectF:$showRectF, drawRectF: ${drawRectF.width()}, ${drawRectF.height()}")
        
        return Pair(imagePath, showRectF)
    }
}
