# 复制粘贴滚动位置修复

## 问题描述
复制粘贴带有列表或待办事项数据后，页面会自动滚动到首屏位置，而复制纯文本则不会。

## 根本原因分析
1. **BaseRichTextEditView.kt 中的 scrollTo 方法被重写**：强制将所有滚动都设置为 `(0, 0)`
2. **列表内容粘贴触发特殊处理**：当复制粘贴列表内容时，`handlePaste` 方法会调用 `setText(builder)`，这会触发文本变化和滚动重置
3. **纯文本粘贴不同路径**：纯文本粘贴不会触发 `handlePaste` 方法，因此不会重置滚动位置

## 修复方案
在 `BaseRichTextEditView.kt` 中添加滚动位置保存和恢复机制，在 `AREditText.java` 的 `handlePaste` 方法中使用这些机制来保持滚动位置。

## 具体修复内容

### 1. BaseRichTextEditView.kt 修改
```kotlin
// 添加字段
private var savedScrollY = 0
private var shouldPreserveScroll = false

// 修改 scrollTo 方法
override fun scrollTo(x: Int, y: Int) {
    if (shouldPreserveScroll) {
        super.scrollTo(0, savedScrollY)
    } else {
        super.scrollTo(0, 0)
    }
}

// 添加方法
fun saveScrollPosition() {
    savedScrollY = scrollY
    shouldPreserveScroll = true
}

fun restoreScrollBehavior() {
    shouldPreserveScroll = false
}
```

### 2. AREditText.java 修改
在 `handlePaste` 方法中：
- 在处理开始时调用 `saveScrollPosition()` 保存当前滚动位置
- 在 `setText` 后调用 `restoreScrollBehavior()` 恢复正常滚动行为
- 在所有退出路径（包括异常处理）都确保调用 `restoreScrollBehavior()`

## 修复效果
- ✅ 复制粘贴列表内容后，页面保持在当前滚动位置
- ✅ 复制粘贴纯文本的行为保持不变
- ✅ 不影响其他滚动相关功能
- ✅ 异常情况下也能正确恢复滚动行为

## 测试验证
1. 在富文本编辑器中输入大量内容，滚动到中间位置
2. 复制包含列表项的内容（如待办事项、有序列表、无序列表）
3. 粘贴内容
4. 验证页面保持在当前滚动位置，而不是跳转到首屏
